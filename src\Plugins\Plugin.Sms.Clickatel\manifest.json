{"name": "Clickatel SMS Plugin", "version": "1.0.0", "description": "SMS plugin for Clickatel provider", "author": "NotificationService Team", "type": "Sms", "provider": "Clickatel", "assemblyName": "Plugin.Sms.Clickatel.dll", "entryPoint": "Plugin.Sms.Clickatel.ClickatelPlugin", "dependencies": [{"name": "Microsoft.Extensions.Http", "version": "9.0.0", "isRequired": true}], "configuration": {"apiUrl": {"type": "string", "description": "Clickatel API URL", "defaultValue": "https://platform.clickatell.com", "isRequired": true, "isSecret": false}, "apiKey": {"type": "string", "description": "Clickatel API Key", "isRequired": true, "isSecret": true}, "from": {"type": "string", "description": "Default sender ID", "isRequired": false, "isSecret": false}, "timeout": {"type": "int", "description": "HTTP timeout in seconds", "defaultValue": 30, "isRequired": false, "isSecret": false}}, "supportedFeatures": ["SendSms", "BulkSms", "MessageStatus"], "minimumFrameworkVersion": "net9.0", "isEnabled": true, "priority": 95, "metadata": {"website": "https://www.clickatell.com", "documentation": "https://docs.clickatell.com/"}}