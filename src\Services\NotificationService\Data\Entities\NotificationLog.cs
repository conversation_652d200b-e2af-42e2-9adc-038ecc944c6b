using System.ComponentModel.DataAnnotations;
using NotificationContract.Enums;

namespace NotificationService.Data.Entities;

public class NotificationLog
{
    [Key]
    public Guid Id { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string MessageId { get; set; } = string.Empty;
    
    [Required]
    public NotificationType Type { get; set; }
    
    [Required]
    [MaxLength(500)]
    public string Recipient { get; set; } = string.Empty;
    
    [MaxLength(200)]
    public string Subject { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    public NotificationStatus Status { get; set; }
    
    [MaxLength(100)]
    public string? Provider { get; set; }
    
    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }
    
    public string? ResponseData { get; set; }
    
    [Required]
    public DateTime CreatedAt { get; set; }
    
    public DateTime? SentAt { get; set; }
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? FailedAt { get; set; }
    
    public int RetryCount { get; set; } = 0;
    
    public DateTime? LastRetryAt { get; set; }
    
    [MaxLength(50)]
    public string? CorrelationId { get; set; }
    
    [MaxLength(100)]
    public string? UserId { get; set; }
    
    public Dictionary<string, object>? Metadata { get; set; }
}

public enum NotificationStatus
{
    Pending = 0,
    Processing = 1,
    Sent = 2,
    Delivered = 3,
    Failed = 4,
    Cancelled = 5,
    Retrying = 6
}
