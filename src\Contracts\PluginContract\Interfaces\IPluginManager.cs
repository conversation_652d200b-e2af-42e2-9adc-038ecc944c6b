using PluginContract.Enums;
using PluginContract.Models;

namespace PluginContract.Interfaces;

/// <summary>
/// Plugin manager for discovering and managing notification plugins
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// Discover and load all available plugins
    /// </summary>
    Task LoadPluginsAsync();
    
    /// <summary>
    /// Get all loaded plugins
    /// </summary>
    IEnumerable<INotificationPlugin> GetAllPlugins();
    
    /// <summary>
    /// Get plugins by type
    /// </summary>
    IEnumerable<INotificationPlugin> GetPluginsByType(PluginType type);
    
    /// <summary>
    /// Get plugin by name
    /// </summary>
    INotificationPlugin? GetPluginByName(string name);
    
    /// <summary>
    /// Get enabled plugins by type
    /// </summary>
    IEnumerable<INotificationPlugin> GetEnabledPluginsByType(PluginType type);
    
    /// <summary>
    /// Enable/disable a plugin
    /// </summary>
    Task SetPluginEnabledAsync(string pluginName, bool enabled);
    
    /// <summary>
    /// Get plugin health status
    /// </summary>
    Task<Dictionary<string, bool>> GetPluginHealthStatusAsync();
}
