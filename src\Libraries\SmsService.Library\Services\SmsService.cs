using SmsService.Library.Interfaces;
using Microsoft.Extensions.Logging;
using NotificationContract.Models;

namespace SmsService.Library.Services;

public sealed class SmsServiceImplementation : ISmsService
{
    private readonly ILogger<SmsServiceImplementation> _logger;

    public SmsServiceImplementation(ILogger<SmsServiceImplementation> logger)
    {
        _logger = logger;
    }

    public async Task<SmsResponse> SendAsync(SmsMessageRequest request)
    {
        try
        {
            if (request is null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrWhiteSpace(request.PhoneNumber))
                throw new ArgumentException("Phone number cannot be null");
                
            if (string.IsNullOrWhiteSpace(request.Message))
                throw new ArgumentException("Message cannot be null");

            _logger.LogInformation("Sending SMS to {PhoneNumber}", request.PhoneNumber);

            // This will be handled by plugins in the new architecture
            // For now, return a placeholder response
            await Task.Delay(100); // Simulate processing

            _logger.LogInformation("SMS sent successfully to {PhoneNumber}", request.PhoneNumber);

            return new SmsResponse(true, MessageId: Guid.NewGuid().ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS to {PhoneNumber}", request.PhoneNumber);
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request)
    {
        var results = new List<SmsResponse>();
        
        foreach (var sms in request.Messages)
        {
            var result = await SendAsync(sms);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkSmsResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        return new MessageStatusResponse(true, Status: "Delivered");
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber)
    {
        return new MessageHistoryResponse(true, Messages: new List<object>());
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        return new SmsResponse(false, ErrorMessage: "Resend not implemented");
    }
}
