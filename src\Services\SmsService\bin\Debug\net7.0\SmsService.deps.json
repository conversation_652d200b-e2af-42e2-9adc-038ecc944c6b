{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"SmsService/1.0.0": {"dependencies": {"KavenegarDotNetCore": "1.0.7", "MassTransit": "8.1.1-develop.1522", "MassTransit.AspNetCore": "7.3.1", "MassTransit.RabbitMQ": "8.1.1-develop.1522", "Microsoft.AspNetCore.OpenApi": "7.0.1", "Serilog.AspNetCore": "7.0.0", "Serilog.Enrichers.Environment": "2.3.0-dev-00793", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.Elasticsearch": "9.0.3", "SmsContract": "1.0.0", "Swashbuckle.AspNetCore": "6.4.0"}, "runtime": {"SmsService.dll": {}}}, "Elasticsearch.Net/7.17.5": {"dependencies": {"Microsoft.CSharp": "4.6.0", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "7.0.1"}, "runtime": {"lib/netstandard2.1/Elasticsearch.Net.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "KavenegarDotNetCore/1.0.7": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Kavenegar.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit/8.1.1-develop.1522": {"dependencies": {"MassTransit.Abstractions": "8.1.1-develop.1522", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "7.0.1", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1", "System.Diagnostics.DiagnosticSource": "7.0.1", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.Json": "7.0.3", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net7.0/MassTransit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.Abstractions/8.1.1-develop.1522": {"runtime": {"lib/net7.0/MassTransit.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.AspNetCore/7.3.1": {"dependencies": {"MassTransit": "8.1.1-develop.1522", "MassTransit.Extensions.DependencyInjection": "7.3.1", "Microsoft.Extensions.Diagnostics.HealthChecks": "7.0.1", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0"}, "runtime": {"lib/netstandard2.0/MassTransit.AspNetCoreIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"dependencies": {"MassTransit": "8.1.1-develop.1522", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/netstandard2.0/MassTransit.ExtensionsDependencyInjectionIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MassTransit.RabbitMQ/8.1.1-develop.1522": {"dependencies": {"MassTransit": "8.1.1-develop.1522", "RabbitMQ.Client": "6.5.0"}, "runtime": {"lib/net7.0/MassTransit.RabbitMqTransport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.OpenApi/7.0.1": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.122.56809"}}}, "Microsoft.CSharp/4.6.0": {}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.3"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks/7.0.1": {"dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "7.0.1", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.122.56809"}}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/7.0.1": {"runtime": {"lib/net7.0/Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.122.56809"}}}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Microsoft.Extensions.Options": "7.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "Microsoft.Extensions.Options/7.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "Microsoft.Extensions.Primitives/7.0.0": {}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.4.3.0", "fileVersion": "1.4.3.0"}}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.2.27524"}}}, "RabbitMQ.Client/6.5.0": {"dependencies": {"System.Memory": "4.5.5", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/netstandard2.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/2.12.0": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Hosting": "7.0.0", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "7.0.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net7.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.Environment/2.3.0-dev-00793": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Enrichers.Environment.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Serilog": "2.12.0", "Serilog.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net7.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "7.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/net7.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Elasticsearch/9.0.3": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/7.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/net7.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.0.1": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Elasticsearch/9.0.3": {"dependencies": {"Elasticsearch.Net": "7.17.5", "Serilog": "2.12.0", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Formatting.Elasticsearch": "9.0.3", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0", "System.Diagnostics.DiagnosticSource": "7.0.1"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.4.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.1": {}, "System.Diagnostics.DiagnosticSource/7.0.1": {"runtime": {"lib/net7.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.323.6910"}}}, "System.Memory/4.5.5": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.3": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net7.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "System.Threading.Channels/7.0.0": {}, "SmsContract/1.0.0": {"runtime": {"SmsContract.dll": {}}}}}, "libraries": {"SmsService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Elasticsearch.Net/7.17.5": {"type": "package", "serviceable": true, "sha512": "sha512-orChsQi1Ceho/NyIylNOn6y4vuGcsbCfMZnCueNN0fzqYEGQmQdPfcVmsR5+3fwpXTgxCdjTUVmqOwvHpCSB+Q==", "path": "elasticsearch.net/7.17.5", "hashPath": "elasticsearch.net.7.17.5.nupkg.sha512"}, "KavenegarDotNetCore/1.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-9joBx8ImirWstKqy1kDJHPKUlNujC1iFjinCjej/z+vWyPpayxGdjDwBS7gTbxrh6Cbmj1F7oibtrbYy4RDOBQ==", "path": "kavenegardotnetcore/1.0.7", "hashPath": "kavenegardotnetcore.1.0.7.nupkg.sha512"}, "MassTransit/8.1.1-develop.1522": {"type": "package", "serviceable": true, "sha512": "sha512-tiJfQE+uVQJQq/safJwmEh7dPVlikK6t6o3H8021TviZyo6DZ448rc7LH7OZBqKS91goPDfqgrHSDboRF3nKEg==", "path": "masstransit/8.1.1-develop.1522", "hashPath": "masstransit.8.1.1-develop.1522.nupkg.sha512"}, "MassTransit.Abstractions/8.1.1-develop.1522": {"type": "package", "serviceable": true, "sha512": "sha512-wgCycEe6fyFR3zrlDPRLEWoIWPEuGE/C3stpqiRKdm141Z6iwjLhPgexT6Ua1uMgDTm0Xyo7uevvBh094UezxA==", "path": "masstransit.abstractions/8.1.1-develop.1522", "hashPath": "masstransit.abstractions.8.1.1-develop.1522.nupkg.sha512"}, "MassTransit.AspNetCore/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-l5zu6mHevPQGP1tfDg4My8dtqH4n2jL22+Y1+GnAs12liQy5gYLcJAwSmCKb1iusjjD9S1tgfdILVkAfShfN8w==", "path": "masstransit.aspnetcore/7.3.1", "hashPath": "masstransit.aspnetcore.7.3.1.nupkg.sha512"}, "MassTransit.Extensions.DependencyInjection/7.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-s1gnkYUngHNP9m2+Jx1vuUAvoVm4KB3oVbtFJUlXNtBBzhRFq1N3Y0w5iiew60ZBLm0j0RQcSRbI0ahMOkL+og==", "path": "masstransit.extensions.dependencyinjection/7.3.1", "hashPath": "masstransit.extensions.dependencyinjection.7.3.1.nupkg.sha512"}, "MassTransit.RabbitMQ/8.1.1-develop.1522": {"type": "package", "serviceable": true, "sha512": "sha512-60jx9C99c9CUJ48r5t0PXKdzep25uGKTtlwkmCCQ2Yuo+BtYZ4kuut9DyBsIhwllcbe4/tRmK0a7SWgdh5l4Fw==", "path": "masstransit.rabbitmq/8.1.1-develop.1522", "hashPath": "masstransit.rabbitmq.8.1.1-develop.1522.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rHaFl7SAHM59bwjcmcJMDyAfISkUDZi68iRSrzOZkGzAct72dqnG+w87+aycpayovZ3YLBZXL+6Oh6hAs5rH1w==", "path": "microsoft.aspnetcore.openapi/7.0.1", "hashPath": "microsoft.aspnetcore.openapi.7.0.1.nupkg.sha512"}, "Microsoft.CSharp/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-kxn3M2rnAGy5N5DgcIwcE8QTePWU/XiYcQVzn9HqTls2NKluVzVSmVWRjK7OUPWbljCXuZxHyhEz9kPRIQeXow==", "path": "microsoft.csharp/4.6.0", "hashPath": "microsoft.csharp.4.6.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tgU4u7bZsoS9MKVRiotVMAwHtbREHr5/5zSEV+JPhg46+ox47Au84E3D2IacAaB0bk5ePNaNieTlPrfjbbRJkg==", "path": "microsoft.extensions.configuration.binder/7.0.0", "hashPath": "microsoft.extensions.configuration.binder.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KbGIGgkJtLNRp//zdISzaADK26tpT0GCYdi6HEctElAy58xP/h67PGd+g9nHH7jOcauWOOVLk+ZCea429SyYRg==", "path": "microsoft.extensions.diagnostics.healthchecks/7.0.1", "hashPath": "microsoft.extensions.diagnostics.healthchecks.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Q6JyXS68pjOZ9U09kRCvzgIK0qfMGLwNekJNAXyUthbcNr+kRgHrgrCqyw3mHoa8R+RvYHmOUSviKSk3qqbDDA==", "path": "microsoft.extensions.diagnostics.healthchecks.abstractions/7.0.1", "hashPath": "microsoft.extensions.diagnostics.healthchecks.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyawiW9ZT/liQb34k9YqBSNPLuuPkrjMgQZ24Y/xXX1RoiBkLUdPMaQTmxhZ5TYu8ZKZ9qayzil75JX95vGQUg==", "path": "microsoft.extensions.fileproviders.abstractions/7.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-43n9Je09z0p/7ViPxfRqs5BUItRLNVh5b6JH40F2Agkh2NBsY/jpNYTtbCcxrHCsA3oRmbR6RJBzUutB4VZvNQ==", "path": "microsoft.extensions.hosting.abstractions/7.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pkeBFx0vqMW/A3aUVHh7MPu3WkBhaVlezhSZeb1c9XD0vUReYH1TLFSy5MxJgZfmz5LZzYoErMorlYZiwpOoNA==", "path": "microsoft.extensions.logging.abstractions/7.0.1", "hashPath": "microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pZRDYdN1FpepOIfHU62QoBQ6zdAoTvnjxFfqAzEd9Jhb2dfhA5i6jeTdgGgcgTWFRC7oT0+3XrbQu4LjvgX1Nw==", "path": "microsoft.extensions.options/7.0.1", "hashPath": "microsoft.extensions.options.7.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "RabbitMQ.Client/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-9hY5HiWPtCla1/l0WmXmLnqoX7iKE3neBQUWnetIJrRpOvTbO//XQfQDh++xgHCshL40Kv/6bR0HDkmJz46twg==", "path": "rabbitmq.client/6.5.0", "hashPath": "rabbitmq.client.6.5.0.nupkg.sha512"}, "Serilog/2.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xaiJLIdu6rYMKfQMYUZgTy8YK7SMZjB4Yk50C/u//Z4OsvxkUfSPJy4nknfvwAC34yr13q7kcyh4grbwhSxyZg==", "path": "serilog/2.12.0", "hashPath": "serilog.2.12.0.nupkg.sha512"}, "Serilog.AspNetCore/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6p6rzOmg/R0EPI/PjJXcAlhCzusW5+xFx8kbsy6mJ6/mHI6wWWPxZbsLPoAYTpMiopKFl7HZhAr//KABGHtBQ==", "path": "serilog.aspnetcore/7.0.0", "hashPath": "serilog.aspnetcore.7.0.0.nupkg.sha512"}, "Serilog.Enrichers.Environment/2.3.0-dev-00793": {"type": "package", "serviceable": true, "sha512": "sha512-ZkxirPHx9e6Sia0fS+12Z3DGrL0Ss0mpM3SfxxyGGHloebKwAAlBGF+AST3EPckC7DAM04oaAsAR0Cw18GxoSg==", "path": "serilog.enrichers.environment/2.3.0-dev-00793", "hashPath": "serilog.enrichers.environment.2.3.0-dev-00793.nupkg.sha512"}, "Serilog.Extensions.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AWsDTs6TeCtyXYDWakzLXCOZA3/IdIfBWBwkYAF0ZvVktVr3E15oYP9pfI7GzKaGVmHaJF9TgFQnFEfcnzEkcw==", "path": "serilog.extensions.hosting/7.0.0", "hashPath": "serilog.extensions.hosting.7.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9faU0zNQqU7I6soVhLUMYaGNpgWv6cKlKb2S5AnS8gXxzW/em5Ladm/6FMrWTnX41cdbdGPOWNAo6adi4WaJ6A==", "path": "serilog.extensions.logging/7.0.0", "hashPath": "serilog.extensions.logging.7.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Formatting.Elasticsearch/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Rii98HYzP6zJfMzmRvbpEpglIwKYNEVrbKse2SofwGiNMSNvoz/U3i2PbtHdGfv2In8s/HQigrDiY7k33GR2KA==", "path": "serilog.formatting.elasticsearch/9.0.3", "hashPath": "serilog.formatting.elasticsearch.9.0.3.nupkg.sha512"}, "Serilog.Settings.Configuration/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MlabSn74uXAGKNv9WWDdGejmsr7cKhsfi98ZtdeK119s4lTLp+hakBtnPm8OvXNUvtXYs2ijXKA0tIy4IMnjWQ==", "path": "serilog.settings.configuration/7.0.0", "hashPath": "serilog.settings.configuration.7.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "path": "serilog.sinks.console/4.0.1", "hashPath": "serilog.sinks.console.4.0.1.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.Elasticsearch/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-k869kDOw58UZmJd5yep4Vl1UA7JokOWrxAC78o6JDVevcg3l6W5KMF+bt2ZaSJt6LGKTi6yBe8l+JBGm4PhCfA==", "path": "serilog.sinks.elasticsearch/9.0.3", "hashPath": "serilog.sinks.elasticsearch.9.0.3.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "path": "swashbuckle.aspnetcore/6.4.0", "hashPath": "swashbuckle.aspnetcore.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-T9SLFxzDp0SreCffRDXSAS5G+lq6E8qP4knHS2IBjwCdx2KEvGnGZsq7gFpselYOda7l6gXsJMD93TQsFj/URA==", "path": "system.diagnostics.diagnosticsource/7.0.1", "hashPath": "system.diagnostics.diagnosticsource.7.0.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-AyjhwXN1zTFeIibHimfJn6eAsZ7rTBib79JQpzg8WAuR/HKDu9JGNHTuu3nbbXQ/bgI+U4z6HtZmCHNXB1QXrQ==", "path": "system.text.json/7.0.3", "hashPath": "system.text.json.7.0.3.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "SmsContract/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}