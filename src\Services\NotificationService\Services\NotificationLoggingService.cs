using Microsoft.EntityFrameworkCore;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotificationService.Data;
using NotificationService.Data.Entities;

namespace NotificationService.Services;

public interface INotificationLoggingService
{
    Task<string> LogNotificationAsync(NotificationType type, string recipient, string subject, string content, string? userId = null, string? correlationId = null, Dictionary<string, object>? metadata = null);
    Task UpdateNotificationStatusAsync(string messageId, NotificationStatus status, string? provider = null, string? errorMessage = null, string? responseData = null);
    Task LogErrorAsync(NotificationType type, string provider, string errorCode, string errorMessage, string? recipient = null, string? messageId = null, string? stackTrace = null, string? requestData = null, int severity = 2);
    Task<NotificationLog?> GetNotificationLogAsync(string messageId);
    Task<List<NotificationLog>> GetNotificationHistoryAsync(string recipient, int skip = 0, int take = 50);
    Task UpdateMetricsAsync(NotificationType type, string provider, bool success, double responseTime);
    Task<List<NotificationMetrics>> GetMetricsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null);
    Task<List<NotificationError>> GetErrorsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null, bool unresolved = false);
}

public class NotificationLoggingService : INotificationLoggingService
{
    private readonly NotificationDbContext _context;
    private readonly ILogger<NotificationLoggingService> _logger;

    public NotificationLoggingService(NotificationDbContext context, ILogger<NotificationLoggingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<string> LogNotificationAsync(NotificationType type, string recipient, string subject, string content, string? userId = null, string? correlationId = null, Dictionary<string, object>? metadata = null)
    {
        try
        {
            var messageId = Guid.NewGuid().ToString();
            var log = new NotificationLog
            {
                Id = Guid.NewGuid(),
                MessageId = messageId,
                Type = type,
                Recipient = recipient,
                Subject = subject,
                Content = content,
                Status = NotificationStatus.Pending,
                CreatedAt = DateTime.UtcNow,
                UserId = userId,
                CorrelationId = correlationId,
                Metadata = metadata
            };

            _context.NotificationLogs.Add(log);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Logged notification {MessageId} for {Type} to {Recipient}", messageId, type, recipient);
            return messageId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging notification for {Type} to {Recipient}", type, recipient);
            return Guid.NewGuid().ToString(); // Return a fallback ID
        }
    }

    public async Task UpdateNotificationStatusAsync(string messageId, NotificationStatus status, string? provider = null, string? errorMessage = null, string? responseData = null)
    {
        try
        {
            var log = await _context.NotificationLogs.FirstOrDefaultAsync(l => l.MessageId == messageId);
            if (log == null)
            {
                _logger.LogWarning("Notification log not found for message ID {MessageId}", messageId);
                return;
            }

            log.Status = status;
            log.Provider = provider ?? log.Provider;
            log.ErrorMessage = errorMessage;
            log.ResponseData = responseData;

            switch (status)
            {
                case NotificationStatus.Sent:
                    log.SentAt = DateTime.UtcNow;
                    break;
                case NotificationStatus.Delivered:
                    log.DeliveredAt = DateTime.UtcNow;
                    break;
                case NotificationStatus.Failed:
                    log.FailedAt = DateTime.UtcNow;
                    break;
                case NotificationStatus.Retrying:
                    log.RetryCount++;
                    log.LastRetryAt = DateTime.UtcNow;
                    break;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated notification {MessageId} status to {Status}", messageId, status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating notification status for {MessageId}", messageId);
        }
    }

    public async Task LogErrorAsync(NotificationType type, string provider, string errorCode, string errorMessage, string? recipient = null, string? messageId = null, string? stackTrace = null, string? requestData = null, int severity = 2)
    {
        try
        {
            var error = new NotificationError
            {
                Id = Guid.NewGuid(),
                Type = type,
                Provider = provider,
                ErrorCode = errorCode,
                ErrorMessage = errorMessage,
                Recipient = recipient,
                MessageId = messageId,
                StackTrace = stackTrace,
                RequestData = requestData,
                OccurredAt = DateTime.UtcNow,
                Severity = severity,
                IsResolved = false
            };

            _context.NotificationErrors.Add(error);
            await _context.SaveChangesAsync();

            _logger.LogError("Logged error {ErrorCode} for {Type} provider {Provider}: {ErrorMessage}", errorCode, type, provider, errorMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging notification error for {Type} provider {Provider}", type, provider);
        }
    }

    public async Task<NotificationLog?> GetNotificationLogAsync(string messageId)
    {
        try
        {
            return await _context.NotificationLogs.FirstOrDefaultAsync(l => l.MessageId == messageId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification log for {MessageId}", messageId);
            return null;
        }
    }

    public async Task<List<NotificationLog>> GetNotificationHistoryAsync(string recipient, int skip = 0, int take = 50)
    {
        try
        {
            return await _context.NotificationLogs
                .Where(l => l.Recipient == recipient)
                .OrderByDescending(l => l.CreatedAt)
                .Skip(skip)
                .Take(take)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting notification history for {Recipient}", recipient);
            return new List<NotificationLog>();
        }
    }

    public async Task UpdateMetricsAsync(NotificationType type, string provider, bool success, double responseTime)
    {
        try
        {
            var today = DateTime.UtcNow.Date;
            var metrics = await _context.NotificationMetrics
                .FirstOrDefaultAsync(m => m.Type == type && m.Provider == provider && m.Date == today);

            if (metrics == null)
            {
                metrics = new NotificationMetrics
                {
                    Id = Guid.NewGuid(),
                    Type = type,
                    Provider = provider,
                    Date = today,
                    CreatedAt = DateTime.UtcNow
                };
                _context.NotificationMetrics.Add(metrics);
            }

            metrics.TotalSent++;
            if (success)
            {
                metrics.TotalDelivered++;
            }
            else
            {
                metrics.TotalFailed++;
            }

            // Update average response time
            var totalResponses = metrics.TotalSent;
            metrics.AverageResponseTime = ((metrics.AverageResponseTime * (totalResponses - 1)) + responseTime) / totalResponses;

            // Update rates
            metrics.SuccessRate = (double)metrics.TotalDelivered / metrics.TotalSent * 100;
            metrics.FailureRate = (double)metrics.TotalFailed / metrics.TotalSent * 100;
            metrics.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating metrics for {Type} provider {Provider}", type, provider);
        }
    }

    public async Task<List<NotificationMetrics>> GetMetricsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var query = _context.NotificationMetrics.AsQueryable();

            if (type.HasValue)
                query = query.Where(m => m.Type == type.Value);

            if (!string.IsNullOrEmpty(provider))
                query = query.Where(m => m.Provider == provider);

            if (from.HasValue)
                query = query.Where(m => m.Date >= from.Value.Date);

            if (to.HasValue)
                query = query.Where(m => m.Date <= to.Value.Date);

            return await query.OrderByDescending(m => m.Date).ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metrics");
            return new List<NotificationMetrics>();
        }
    }

    public async Task<List<NotificationError>> GetErrorsAsync(NotificationType? type = null, string? provider = null, DateTime? from = null, DateTime? to = null, bool unresolved = false)
    {
        try
        {
            var query = _context.NotificationErrors.AsQueryable();

            if (type.HasValue)
                query = query.Where(e => e.Type == type.Value);

            if (!string.IsNullOrEmpty(provider))
                query = query.Where(e => e.Provider == provider);

            if (from.HasValue)
                query = query.Where(e => e.OccurredAt >= from.Value);

            if (to.HasValue)
                query = query.Where(e => e.OccurredAt <= to.Value);

            if (unresolved)
                query = query.Where(e => !e.IsResolved);

            return await query.OrderByDescending(e => e.OccurredAt).ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting errors");
            return new List<NotificationError>();
        }
    }
}
