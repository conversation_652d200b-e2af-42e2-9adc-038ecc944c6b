using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PluginContract.Attributes;
using PluginContract.Enums;
using PluginContract.Interfaces;
using PluginContract.Models;

namespace PluginCore.Services;

public sealed class PluginManager : IPluginManager
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly ILogger<PluginManager> _logger;
    private readonly List<INotificationPlugin> _plugins = new();
    private readonly Dictionary<string, bool> _pluginEnabledStatus = new();

    public PluginManager(
        IServiceProvider serviceProvider,
        IConfiguration configuration,
        ILogger<PluginManager> logger)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task LoadPluginsAsync()
    {
        _logger.LogInformation("Starting plugin discovery...");
        
        var pluginDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins");
        if (!Directory.Exists(pluginDirectory))
        {
            _logger.LogWarning("Plugin directory not found: {PluginDirectory}", pluginDirectory);
            return;
        }

        var pluginFiles = Directory.GetFiles(pluginDirectory, "*.dll", SearchOption.AllDirectories);
        
        foreach (var pluginFile in pluginFiles)
        {
            try
            {
                await LoadPluginFromAssemblyAsync(pluginFile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load plugin from {PluginFile}", pluginFile);
            }
        }

        _logger.LogInformation("Plugin discovery completed. Loaded {PluginCount} plugins", _plugins.Count);
    }

    private async Task LoadPluginFromAssemblyAsync(string assemblyPath)
    {
        var assembly = Assembly.LoadFrom(assemblyPath);
        var pluginTypes = assembly.GetTypes()
            .Where(t => t.GetCustomAttribute<NotificationPluginAttribute>() != null)
            .Where(t => typeof(INotificationPlugin).IsAssignableFrom(t))
            .Where(t => !t.IsAbstract && !t.IsInterface);

        foreach (var pluginType in pluginTypes)
        {
            try
            {
                var plugin = (INotificationPlugin)Activator.CreateInstance(pluginType)!;
                
                // Initialize plugin
                await plugin.InitializeAsync(_configuration);
                
                // Validate configuration
                var isValid = await plugin.ValidateConfigurationAsync(_configuration);
                if (!isValid)
                {
                    _logger.LogWarning("Plugin {PluginName} configuration validation failed", plugin.PluginInfo.Name);
                    continue;
                }

                _plugins.Add(plugin);
                _pluginEnabledStatus[plugin.PluginInfo.Name] = plugin.PluginInfo.IsEnabled;
                
                _logger.LogInformation("Loaded plugin: {PluginName} v{Version}", 
                    plugin.PluginInfo.Name, plugin.PluginInfo.Version);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to instantiate plugin {PluginType}", pluginType.Name);
            }
        }
    }

    public IEnumerable<INotificationPlugin> GetAllPlugins()
    {
        return _plugins.AsReadOnly();
    }

    public IEnumerable<INotificationPlugin> GetPluginsByType(PluginType type)
    {
        return _plugins.Where(p => p.PluginInfo.Type == type);
    }

    public INotificationPlugin? GetPluginByName(string name)
    {
        return _plugins.FirstOrDefault(p => p.PluginInfo.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
    }

    public IEnumerable<INotificationPlugin> GetEnabledPluginsByType(PluginType type)
    {
        return _plugins.Where(p => p.PluginInfo.Type == type && 
                                  _pluginEnabledStatus.GetValueOrDefault(p.PluginInfo.Name, true));
    }

    public Task SetPluginEnabledAsync(string pluginName, bool enabled)
    {
        if (_pluginEnabledStatus.ContainsKey(pluginName))
        {
            _pluginEnabledStatus[pluginName] = enabled;
            _logger.LogInformation("Plugin {PluginName} {Status}", pluginName, enabled ? "enabled" : "disabled");
        }
        
        return Task.CompletedTask;
    }

    public async Task<Dictionary<string, bool>> GetPluginHealthStatusAsync()
    {
        var healthStatus = new Dictionary<string, bool>();
        
        foreach (var plugin in _plugins)
        {
            try
            {
                var isHealthy = await plugin.HealthCheckAsync();
                healthStatus[plugin.PluginInfo.Name] = isHealthy;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed for plugin {PluginName}", plugin.PluginInfo.Name);
                healthStatus[plugin.PluginInfo.Name] = false;
            }
        }
        
        return healthStatus;
    }
}
