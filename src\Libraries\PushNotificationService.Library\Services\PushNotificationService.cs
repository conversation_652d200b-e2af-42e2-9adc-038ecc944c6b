using PushNotificationService.Library.Interfaces;
using Microsoft.Extensions.Logging;
using NotificationContract.Models;
using FirebaseAdmin.Messaging;

namespace PushNotificationService.Library.Services;

public sealed class PushNotificationServiceImplementation : IPushNotificationService
{
    private readonly ILogger<PushNotificationServiceImplementation> _logger;

    public PushNotificationServiceImplementation(ILogger<PushNotificationServiceImplementation> logger)
    {
        _logger = logger;
    }

    public async Task<PushResponse> SendAsync(PushMessageRequest request)
    {
        try
        {
            if (request is null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrWhiteSpace(request.DeviceToken))
                throw new ArgumentException("Device token cannot be null");
                
            if (string.IsNullOrWhiteSpace(request.Body))
                throw new ArgumentException("Message body cannot be null");

            _logger.LogInformation("Sending push notification to device {DeviceToken}", request.DeviceToken);

            var message = new Message()
            {
                Token = request.DeviceToken,
                Notification = new Notification()
                {
                    Title = request.Title,
                    Body = request.Body
                },
                Data = request.Data
            };

            var response = await FirebaseMessaging.DefaultInstance.SendAsync(message);

            _logger.LogInformation("Push notification sent successfully to device {DeviceToken}, MessageId: {MessageId}", 
                request.DeviceToken, response);

            return new PushResponse(true, MessageId: response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send push notification to device {DeviceToken}", request.DeviceToken);
            return new PushResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request)
    {
        var results = new List<PushResponse>();
        
        foreach (var push in request.Messages)
        {
            var result = await SendAsync(push);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkPushResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        return new MessageStatusResponse(true, Status: "Delivered");
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken)
    {
        return new MessageHistoryResponse(true, Messages: new List<object>());
    }

    public async Task<PushResponse> ResendMessageAsync(string messageId)
    {
        return new PushResponse(false, ErrorMessage: "Resend not implemented");
    }
}
