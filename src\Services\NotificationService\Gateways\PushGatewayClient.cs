using NotificationContract.Models;
using NotificationService.Gateways.Interfaces;
using System.Text;
using System.Text.Json;

namespace NotificationService.Gateways;

public class PushGatewayClient : IPushGateway
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<PushGatewayClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public PushGatewayClient(HttpClient httpClient, ILogger<PushGatewayClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<PushResponse> SendPushAsync(PushMessageRequest message)
    {
        try
        {
            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/push/send", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<PushResponse>(responseContent, _jsonOptions) 
                       ?? new PushResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Push service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new PushResponse(false, $"Push service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling push service");
            return new PushResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public async Task<BulkPushResponse> SendBulkPushAsync(BulkPushRequest bulk)
    {
        try
        {
            var json = JsonSerializer.Serialize(bulk, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/push/send/bulk", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<BulkPushResponse>(responseContent, _jsonOptions) 
                       ?? new BulkPushResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Bulk push service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new BulkPushResponse(false, $"Bulk push service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling bulk push service");
            return new BulkPushResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/push/status?messageId={messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MessageStatusResponse>(responseContent, _jsonOptions) 
                       ?? new MessageStatusResponse { MessageId = messageId, Status = "Unknown" };
            }
            
            return new MessageStatusResponse { MessageId = messageId, Status = "Error" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push status");
            return new MessageStatusResponse { MessageId = messageId, Status = "Error" };
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/push/history?deviceToken={deviceToken}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MessageHistoryResponse>(responseContent, _jsonOptions) 
                       ?? new MessageHistoryResponse { Recipient = deviceToken };
            }
            
            return new MessageHistoryResponse { Recipient = deviceToken };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push history");
            return new MessageHistoryResponse { Recipient = deviceToken };
        }
    }

    public async Task<PushResponse> ResendMessageAsync(string messageId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/push/resend?messageId={messageId}", null);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<PushResponse>(responseContent, _jsonOptions) 
                       ?? new PushResponse(false, "Failed to deserialize response");
            }
            
            return new PushResponse(false, $"Resend service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending push notification");
            return new PushResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    // Admin and metrics methods - simplified implementations
    public IEnumerable<ProviderInfo> GetAvailableProviders() => Enumerable.Empty<ProviderInfo>();
    public void SwitchProvider(string providerKey) { }
    public async Task<PushResponse> SendTestMessageAsync(string to) => new PushResponse(false, "Not implemented");
    public void ReloadProviders() { }
    public void UpdateProviderConfiguration(string providerKey, Dictionary<string, string> config) { }
    public async Task<SummaryMetricsResponse> GetSummaryMetricsAsync() => new SummaryMetricsResponse();
    public async Task<DetailedMetricsResponse> GetDetailedMetricsAsync(DateTime? from, DateTime? to) => new DetailedMetricsResponse();
    public async Task<ErrorMetricsResponse> GetErrorMetricsAsync() => new ErrorMetricsResponse();
    public async Task<MonthlyStatisticsResponse> GetMonthlyStatisticsAsync() => new MonthlyStatisticsResponse();
}
