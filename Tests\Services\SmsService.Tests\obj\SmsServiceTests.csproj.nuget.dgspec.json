{"format": 1, "restore": {"/Users/<USER>/Projects/NotificationService/Tests/Services/SmsServiceTests/SmsServiceTests.csproj": {}}, "projects": {"/Users/<USER>/Projects/NotificationService/src/Contracts/SmsContract/SmsContract.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/NotificationService/src/Contracts/SmsContract/SmsContract.csproj", "projectName": "SmsContract", "projectPath": "/Users/<USER>/Projects/NotificationService/src/Contracts/SmsContract/SmsContract.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/NotificationService/src/Contracts/SmsContract/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.101/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Projects/NotificationService/src/Services/SmsService/SmsService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/NotificationService/src/Services/SmsService/SmsService.csproj", "projectName": "SmsService", "projectPath": "/Users/<USER>/Projects/NotificationService/src/Services/SmsService/SmsService.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/NotificationService/src/Services/SmsService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"/Users/<USER>/Projects/NotificationService/src/Contracts/SmsContract/SmsContract.csproj": {"projectPath": "/Users/<USER>/Projects/NotificationService/src/Contracts/SmsContract/SmsContract.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"KavenegarDotNetCore": {"target": "Package", "version": "[1.0.7, )"}, "MassTransit": {"target": "Package", "version": "[8.1.1-develop.1522, )"}, "MassTransit.AspNetCore": {"target": "Package", "version": "[7.3.1, )"}, "MassTransit.RabbitMQ": {"target": "Package", "version": "[8.1.1-develop.1522, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[7.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.101/RuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Projects/NotificationService/Tests/Services/SmsServiceTests/SmsServiceTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/NotificationService/Tests/Services/SmsServiceTests/SmsServiceTests.csproj", "projectName": "SmsServiceTests", "projectPath": "/Users/<USER>/Projects/NotificationService/Tests/Services/SmsServiceTests/SmsServiceTests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/NotificationService/Tests/Services/SmsServiceTests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"/Users/<USER>/Projects/NotificationService/src/Services/SmsService/SmsService.csproj": {"projectPath": "/Users/<USER>/Projects/NotificationService/src/Services/SmsService/SmsService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.Extensions.Options": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.3.2, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.2, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.101/RuntimeIdentifierGraph.json"}}}}}