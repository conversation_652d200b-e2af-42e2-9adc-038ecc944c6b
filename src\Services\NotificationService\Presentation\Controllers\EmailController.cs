using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotificationService.Data.Entities;
using NotificationService.Gateways.Interfaces;
using NotificationService.Services;
using EmailContract.Models;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class EmailController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly ILogger<EmailController> _logger;

    public EmailController(
        IEmailGateway emailGateway,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        ILogger<EmailController> logger)
    {
        _emailGateway = emailGateway;
        _loggingService = loggingService;
        _queueService = queueService;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<ActionResult> SendEmail([FromBody] SendEmailRequest message)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();
            
            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.Email,
                message.ToEmail,
                message.Subject,
                message.Body,
                message.UserId,
                correlationId);

            // Queue the notification
            var queueItem = new NotificationQueueItem
            {
                MessageId = messageId,
                Type = NotificationType.Email,
                Recipient = message.ToEmail,
                Subject = message.Subject,
                Content = message.Body,
                UserId = message.UserId,
                CorrelationId = correlationId,
                Priority = NotificationPriority.Normal
            };

            var queueId = await _queueService.QueueNotificationAsync(queueItem);
            
            _logger.LogInformation("Queued email notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            
            return Ok(new { 
                Success = true, 
                MessageId = messageId, 
                QueueId = queueId, 
                CorrelationId = correlationId 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing email request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    [HttpPost("send/direct")]
    public async Task<ActionResult> SendEmailDirect([FromBody] SendEmailRequest message)
    {
        var result = await _emailGateway.SendEmailAsync(message);
        return Ok(result);
    }

    [HttpGet("status")]
    public async Task<ActionResult> GetEmailStatus([FromQuery] string messageId)
    {
        var status = await _emailGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("history")]
    public async Task<ActionResult> GetEmailHistory([FromQuery] string email)
    {
        var history = await _emailGateway.GetMessageHistoryAsync(email);
        return Ok(history);
    }

    [HttpPost("resend")]
    public async Task<ActionResult> ResendEmail([FromQuery] string messageId)
    {
        var result = await _emailGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    [HttpGet("providers")]
    public ActionResult GetEmailProviders() => Ok(_emailGateway.GetAvailableProviders());

    [HttpPost("switch-provider")]
    public ActionResult SwitchEmailProvider([FromQuery] string providerKey)
    {
        _emailGateway.SwitchProvider(providerKey);
        return Ok(new { provider = providerKey });
    }

    [HttpPost("test")]
    public async Task<ActionResult> TestEmail([FromQuery] string to)
    {
        var result = await _emailGateway.SendTestMessageAsync(to);
        return Ok(result);
    }

    [HttpPost("reload-providers")]
    public ActionResult ReloadEmailProviders()
    {
        _emailGateway.ReloadProviders();
        return Ok();
    }

    [HttpPost("config/update")]
    public ActionResult UpdateEmailConfig([FromQuery] string providerKey, [FromBody] Dictionary<string, string> config)
    {
        _emailGateway.UpdateProviderConfiguration(providerKey, config);
        return Ok();
    }

    [HttpGet("metrics/summary")]
    public async Task<ActionResult> GetEmailSummary() => Ok(await _emailGateway.GetSummaryMetricsAsync());

    [HttpGet("metrics/detailed")]
    public async Task<ActionResult> GetEmailDetailed([FromQuery] DateTime? from, [FromQuery] DateTime? to)
    {
        return Ok(await _emailGateway.GetDetailedMetricsAsync(from, to));
    }

    [HttpGet("metrics/errors")]
    public async Task<ActionResult> GetEmailErrors() => Ok(await _emailGateway.GetErrorMetricsAsync());

    [HttpGet("metrics/monthly")]
    public async Task<ActionResult> GetEmailMonthlyStats() => Ok(await _emailGateway.GetMonthlyStatisticsAsync());
}
