using EmailContract.Models;
using EmailService.Library.Interfaces;
using EmailService.Library.Mappers;
using EmailService.Library.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using NotificationContract.Models;

namespace EmailService.Library.Services;

public sealed class EmailSenderService : IEmailService
{
    private readonly ISmtpClient _smtpClient;
    private readonly MailSetting _mailSetting;
    private readonly ILogger<EmailSenderService> _logger;

    public EmailSenderService(ISmtpClient smtpClient, IOptions<EmailServiceSettings> config, ILogger<EmailSenderService> logger)
    {
        _smtpClient = smtpClient;
        _mailSetting = config.Value.MailSettings;
        _logger = logger;
    }

    public async Task<EmailResponse> SendAsync(EmailMessageRequest request)
    {
        try
        {
            if (request is null)
                throw new ArgumentNullException(nameof(request));

            if (string.IsNullOrWhiteSpace(request.To))
                throw new ArgumentException("To email address can't be null");

            if (string.IsNullOrWhiteSpace(request.Body))
                throw new ArgumentException("Body can't be null");

            _logger.LogInformation("Sending email to {To} with subject {Subject}", request.To, request.Subject);

            await _smtpClient.SendAsync(request.MapToMailMessage(_mailSetting));

            _logger.LogInformation("Email sent successfully to {To}", request.To);

            return new EmailResponse(true, MessageId: Guid.NewGuid().ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {To}", request.To);
            return new EmailResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkEmailResponse> SendBulkAsync(BulkEmailRequest request)
    {
        var results = new List<EmailResponse>();
        
        foreach (var email in request.Emails)
        {
            var result = await SendAsync(email);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkEmailResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        // Implementation would depend on email provider capabilities
        return new MessageStatusResponse(true, Status: "Delivered");
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress)
    {
        // Implementation would depend on email provider capabilities
        return new MessageHistoryResponse(true, Messages: new List<object>());
    }

    public async Task<EmailResponse> ResendMessageAsync(string messageId)
    {
        // Implementation would depend on email provider capabilities
        return new EmailResponse(false, ErrorMessage: "Resend not implemented");
    }
}
