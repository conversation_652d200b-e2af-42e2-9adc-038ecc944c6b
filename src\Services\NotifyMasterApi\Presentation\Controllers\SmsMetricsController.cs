using Microsoft.AspNetCore.Mvc;
using SmsService.Library.Interfaces;
using NotificationService.Services;
using NotificationContract.Enums;

namespace NotificationService.Controllers;

/// <summary>
/// SMS service metrics and analytics controller
/// </summary>
[ApiController]
[Route("api/sms/metrics")]
[Produces("application/json")]
[Tags("SMS Metrics")]
public class SmsMetricsController : ControllerBase
{
    private readonly ISmsService _smsService;
    private readonly INotificationLoggingService _loggingService;
    private readonly ILogger<SmsMetricsController> _logger;

    public SmsMetricsController(
        ISmsService smsService,
        INotificationLoggingService loggingService,
        ILogger<SmsMetricsController> logger)
    {
        _smsService = smsService;
        _loggingService = loggingService;
        _logger = logger;
    }

    /// <summary>
    /// Get SMS service summary metrics
    /// </summary>
    /// <returns>Summary metrics including total sent, success rate, and recent activity</returns>
    /// <response code="200">Summary metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("summary")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetSummaryMetrics()
    {
        try
        {
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Sms);
            var serviceMetrics = await _smsService.GetSummaryMetricsAsync();
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceMetrics,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS summary metrics");
            return StatusCode(500, new { Error = "Failed to get summary metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get detailed SMS metrics for a specific time period
    /// </summary>
    /// <param name="from">Start date for metrics (optional)</param>
    /// <param name="to">End date for metrics (optional)</param>
    /// <param name="provider">Filter by specific SMS provider (optional)</param>
    /// <returns>Detailed metrics including hourly/daily breakdowns</returns>
    /// <response code="200">Detailed metrics retrieved successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("detailed")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDetailedMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? provider = null)
    {
        try
        {
            if (from.HasValue && to.HasValue && from > to)
            {
                return BadRequest(new { Error = "Start date cannot be after end date" });
            }

            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Sms, provider, from, to);
            var serviceMetrics = await _smsService.GetDetailedMetricsAsync(from, to);
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceMetrics,
                Period = new { From = from, To = to },
                Provider = provider,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed SMS metrics");
            return StatusCode(500, new { Error = "Failed to get detailed metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get SMS error metrics and failure analysis
    /// </summary>
    /// <param name="from">Start date for error analysis (optional)</param>
    /// <param name="to">End date for error analysis (optional)</param>
    /// <param name="provider">Filter by specific SMS provider (optional)</param>
    /// <param name="unresolved">Show only unresolved errors</param>
    /// <returns>Error metrics and failure patterns</returns>
    /// <response code="200">Error metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("errors")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetErrorMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? provider = null,
        [FromQuery] bool unresolved = false)
    {
        try
        {
            var errors = await _loggingService.GetErrorsAsync(NotificationType.Sms, provider, from, to, unresolved);
            var serviceErrors = await _smsService.GetErrorMetricsAsync();
            
            return Ok(new
            {
                Database = errors,
                Service = serviceErrors,
                Period = new { From = from, To = to },
                Provider = provider,
                UnresolvedOnly = unresolved,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS error metrics");
            return StatusCode(500, new { Error = "Failed to get error metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get monthly SMS statistics and trends
    /// </summary>
    /// <param name="months">Number of months to include (default: 12)</param>
    /// <param name="provider">Filter by specific SMS provider (optional)</param>
    /// <returns>Monthly statistics and trend analysis</returns>
    /// <response code="200">Monthly statistics retrieved successfully</response>
    /// <response code="400">Invalid months parameter</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("monthly")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetMonthlyStatistics(
        [FromQuery] int months = 12,
        [FromQuery] string? provider = null)
    {
        try
        {
            if (months <= 0 || months > 24)
            {
                return BadRequest(new { Error = "Months must be between 1 and 24" });
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMonths(-months);
            
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Sms, provider, startDate, endDate);
            var serviceStats = await _smsService.GetMonthlyStatisticsAsync();
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceStats,
                Period = new { StartDate = startDate, EndDate = endDate, Months = months },
                Provider = provider,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monthly SMS statistics");
            return StatusCode(500, new { Error = "Failed to get monthly statistics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get real-time SMS service performance metrics
    /// </summary>
    /// <returns>Real-time performance data including response times and throughput</returns>
    /// <response code="200">Performance metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("performance")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetPerformanceMetrics()
    {
        try
        {
            // Get recent performance data (last hour)
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-1);
            
            var recentMetrics = await _loggingService.GetMetricsAsync(NotificationType.Sms, null, startTime, endTime);
            
            return Ok(new
            {
                RecentActivity = recentMetrics,
                Period = new { StartTime = startTime, EndTime = endTime },
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS performance metrics");
            return StatusCode(500, new { Error = "Failed to get performance metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get SMS delivery rate analysis by provider and country
    /// </summary>
    /// <param name="days">Number of days to analyze (default: 30)</param>
    /// <param name="country">Filter by country code (optional)</param>
    /// <returns>Delivery rate comparison across providers and countries</returns>
    /// <response code="200">Delivery rate analysis retrieved successfully</response>
    /// <response code="400">Invalid days parameter</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("delivery-rates")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDeliveryRateAnalysis(
        [FromQuery] int days = 30,
        [FromQuery] string? country = null)
    {
        try
        {
            if (days <= 0 || days > 365)
            {
                return BadRequest(new { Error = "Days must be between 1 and 365" });
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-days);
            
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Sms, null, startDate, endDate);
            
            return Ok(new
            {
                DeliveryAnalysis = metrics,
                Period = new { StartDate = startDate, EndDate = endDate, Days = days },
                Country = country,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS delivery rate analysis");
            return StatusCode(500, new { Error = "Failed to get delivery rate analysis", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get SMS cost analysis and billing metrics
    /// </summary>
    /// <param name="from">Start date for cost analysis (optional)</param>
    /// <param name="to">End date for cost analysis (optional)</param>
    /// <param name="provider">Filter by specific SMS provider (optional)</param>
    /// <returns>Cost breakdown and billing information</returns>
    /// <response code="200">Cost analysis retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("costs")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetCostAnalysis(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? provider = null)
    {
        try
        {
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Sms, provider, from, to);
            
            return Ok(new
            {
                CostAnalysis = metrics,
                Period = new { From = from, To = to },
                Provider = provider,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS cost analysis");
            return StatusCode(500, new { Error = "Failed to get cost analysis", Message = ex.Message });
        }
    }
}
