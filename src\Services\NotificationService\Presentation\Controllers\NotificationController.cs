using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotificationService.Data.Entities;
using NotificationService.Gateways.Interfaces;
using NotificationService.Services;
using System.Diagnostics;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NotificationsController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly ISmsGateway _smsGateway;
    private readonly IPushGateway _pushGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly ILogger<NotificationsController> _logger;

    public NotificationsController(
        IEmailGateway emailGateway,
        ISmsGateway smsGateway,
        IPushGateway pushGateway,
        INotificationLoggingService loggingService,
        ILogger<NotificationsController> logger)
    {
        _emailGateway = emailGateway;
        _smsGateway = smsGateway;
        _pushGateway = pushGateway;
        _loggingService = loggingService;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<ActionResult> Send(SendNotificationRequest request)
    {
        var results = new List<object>();
        var correlationId = Guid.NewGuid().ToString();

        // Send email if requested
        if (request.NotificationTypes.Contains(NotificationType.Email))
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Email,
                    request.ReceptorMail,
                    request.Subject ?? "Notification",
                    request.Body,
                    request.UserId,
                    correlationId);

                var emailRequest = new EmailMessageRequest
                {
                    To = request.ReceptorMail,
                    Subject = request.Subject ?? "Notification",
                    Body = request.Body,
                    IsHtml = true
                };

                var emailResult = await _emailGateway.SendEmailAsync(emailRequest);
                stopwatch.Stop();

                await _loggingService.UpdateNotificationStatusAsync(
                    messageId,
                    emailResult.IsSuccess ? NotificationStatus.Sent : NotificationStatus.Failed,
                    "EmailService",
                    emailResult.IsSuccess ? null : emailResult.ErrorMessage,
                    emailResult.IsSuccess ? "Email sent successfully" : null);

                await _loggingService.UpdateMetricsAsync(NotificationType.Email, "EmailService", emailResult.IsSuccess, stopwatch.ElapsedMilliseconds);

                if (!emailResult.IsSuccess)
                {
                    await _loggingService.LogErrorAsync(NotificationType.Email, "EmailService", "SEND_FAILED", emailResult.ErrorMessage ?? "Unknown error", request.ReceptorMail, messageId);
                }

                results.Add(new { Type = "Email", Success = emailResult.IsSuccess, Error = emailResult.ErrorMessage, MessageId = messageId });
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error sending email notification");
                await _loggingService.LogErrorAsync(NotificationType.Email, "EmailService", "EXCEPTION", ex.Message, request.ReceptorMail, stackTrace: ex.StackTrace);
                results.Add(new { Type = "Email", Success = false, Error = ex.Message });
            }
        }

        // Send SMS if requested
        if (request.NotificationTypes.Contains(NotificationType.Sms))
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Sms,
                    request.ReceptorPhoneNumber,
                    "",
                    request.Body,
                    request.UserId,
                    correlationId);

                var smsRequest = new SmsMessageRequest
                {
                    PhoneNumber = request.ReceptorPhoneNumber,
                    Message = request.Body
                };

                var smsResult = await _smsGateway.SendSmsAsync(smsRequest);
                stopwatch.Stop();

                await _loggingService.UpdateNotificationStatusAsync(
                    messageId,
                    smsResult.IsSuccess ? NotificationStatus.Sent : NotificationStatus.Failed,
                    "SmsService",
                    smsResult.IsSuccess ? null : smsResult.ErrorMessage,
                    smsResult.IsSuccess ? "SMS sent successfully" : null);

                await _loggingService.UpdateMetricsAsync(NotificationType.Sms, "SmsService", smsResult.IsSuccess, stopwatch.ElapsedMilliseconds);

                if (!smsResult.IsSuccess)
                {
                    await _loggingService.LogErrorAsync(NotificationType.Sms, "SmsService", "SEND_FAILED", smsResult.ErrorMessage ?? "Unknown error", request.ReceptorPhoneNumber, messageId);
                }

                results.Add(new { Type = "SMS", Success = smsResult.IsSuccess, Error = smsResult.ErrorMessage, MessageId = messageId });
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error sending SMS notification");
                await _loggingService.LogErrorAsync(NotificationType.Sms, "SmsService", "EXCEPTION", ex.Message, request.ReceptorPhoneNumber, stackTrace: ex.StackTrace);
                results.Add(new { Type = "SMS", Success = false, Error = ex.Message });
            }
        }

        // Send push notification if requested
        if (request.NotificationTypes.Contains(NotificationType.PushMessage))
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.PushMessage,
                    request.DeviceToken,
                    request.Subject ?? "Notification",
                    request.Body,
                    request.UserId,
                    correlationId);

                var pushRequest = new PushMessageRequest
                {
                    DeviceToken = request.DeviceToken,
                    Title = request.Subject ?? "Notification",
                    Body = request.Body
                };

                var pushResult = await _pushGateway.SendPushAsync(pushRequest);
                stopwatch.Stop();

                await _loggingService.UpdateNotificationStatusAsync(
                    messageId,
                    pushResult.IsSuccess ? NotificationStatus.Sent : NotificationStatus.Failed,
                    "PushService",
                    pushResult.IsSuccess ? null : pushResult.ErrorMessage,
                    pushResult.IsSuccess ? "Push notification sent successfully" : null);

                await _loggingService.UpdateMetricsAsync(NotificationType.PushMessage, "PushService", pushResult.IsSuccess, stopwatch.ElapsedMilliseconds);

                if (!pushResult.IsSuccess)
                {
                    await _loggingService.LogErrorAsync(NotificationType.PushMessage, "PushService", "SEND_FAILED", pushResult.ErrorMessage ?? "Unknown error", request.DeviceToken, messageId);
                }

                results.Add(new { Type = "Push", Success = pushResult.IsSuccess, Error = pushResult.ErrorMessage, MessageId = messageId });
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error sending push notification");
                await _loggingService.LogErrorAsync(NotificationType.PushMessage, "PushService", "EXCEPTION", ex.Message, request.DeviceToken, stackTrace: ex.StackTrace);
                results.Add(new { Type = "Push", Success = false, Error = ex.Message });
            }
        }

        return Ok(new { Results = results, CorrelationId = correlationId });
    }

    // Email endpoints
    [HttpPost("email/send")]
    public async Task<ActionResult> SendEmail([FromBody] EmailMessageRequest message)
    {
        var result = await _emailGateway.SendEmailAsync(message);
        return Ok(result);
    }

    [HttpPost("email/send/bulk")]
    public async Task<ActionResult> SendBulkEmail([FromBody] BulkEmailRequest bulk)
    {
        var result = await _emailGateway.SendBulkEmailAsync(bulk);
        return Ok(result);
    }

    [HttpGet("email/status")]
    public async Task<ActionResult> GetEmailStatus([FromQuery] string messageId)
    {
        var status = await _emailGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("email/history")]
    public async Task<ActionResult> GetEmailHistory([FromQuery] string email)
    {
        var history = await _emailGateway.GetMessageHistoryAsync(email);
        return Ok(history);
    }

    [HttpPost("email/resend")]
    public async Task<ActionResult> ResendEmail([FromQuery] string messageId)
    {
        var result = await _emailGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    // SMS endpoints
    [HttpPost("sms/send")]
    public async Task<ActionResult> SendSms([FromBody] SmsMessageRequest message)
    {
        var result = await _smsGateway.SendSmsAsync(message);
        return Ok(result);
    }

    [HttpPost("sms/send/bulk")]
    public async Task<ActionResult> SendBulkSms([FromBody] BulkSmsRequest bulk)
    {
        var result = await _smsGateway.SendBulkSmsAsync(bulk);
        return Ok(result);
    }

    [HttpGet("sms/status")]
    public async Task<ActionResult> GetSmsStatus([FromQuery] string messageId)
    {
        var status = await _smsGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("sms/history")]
    public async Task<ActionResult> GetSmsHistory([FromQuery] string phoneNumber)
    {
        var history = await _smsGateway.GetMessageHistoryAsync(phoneNumber);
        return Ok(history);
    }

    [HttpPost("sms/resend")]
    public async Task<ActionResult> ResendSms([FromQuery] string messageId)
    {
        var result = await _smsGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    // Push notification endpoints
    [HttpPost("push/send")]
    public async Task<ActionResult> SendPush([FromBody] PushMessageRequest message)
    {
        var result = await _pushGateway.SendPushAsync(message);
        return Ok(result);
    }

    [HttpPost("push/send/bulk")]
    public async Task<ActionResult> SendBulkPush([FromBody] BulkPushRequest bulk)
    {
        var result = await _pushGateway.SendBulkPushAsync(bulk);
        return Ok(result);
    }

    [HttpGet("push/status")]
    public async Task<ActionResult> GetPushStatus([FromQuery] string messageId)
    {
        var status = await _pushGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("push/history")]
    public async Task<ActionResult> GetPushHistory([FromQuery] string deviceToken)
    {
        var history = await _pushGateway.GetMessageHistoryAsync(deviceToken);
        return Ok(history);
    }

    [HttpPost("push/resend")]
    public async Task<ActionResult> ResendPush([FromQuery] string messageId)
    {
        var result = await _pushGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    // Admin endpoints
    [HttpGet("email/admin/providers")]
    public ActionResult GetEmailProviders() => Ok(_emailGateway.GetAvailableProviders());

    [HttpPost("email/admin/switch")]
    public ActionResult SwitchEmailProvider([FromQuery] string providerKey)
    {
        _emailGateway.SwitchProvider(providerKey);
        return Ok(new { provider = providerKey });
    }

    [HttpPost("email/admin/test")]
    public async Task<ActionResult> TestEmail([FromQuery] string to)
    {
        var result = await _emailGateway.SendTestMessageAsync(to);
        return Ok(result);
    }

    [HttpPost("email/admin/reload")]
    public ActionResult ReloadEmailProviders()
    {
        _emailGateway.ReloadProviders();
        return Ok();
    }

    [HttpPost("email/admin/config/update")]
    public ActionResult UpdateEmailConfig([FromQuery] string providerKey, [FromBody] Dictionary<string, string> config)
    {
        _emailGateway.UpdateProviderConfiguration(providerKey, config);
        return Ok();
    }

    // Metrics endpoints
    [HttpGet("email/metrics/summary")]
    public async Task<ActionResult> GetEmailSummary() => Ok(await _emailGateway.GetSummaryMetricsAsync());

    [HttpGet("email/metrics/detailed")]
    public async Task<ActionResult> GetEmailDetailed([FromQuery] DateTime? from, [FromQuery] DateTime? to)
    {
        return Ok(await _emailGateway.GetDetailedMetricsAsync(from, to));
    }

    [HttpGet("email/metrics/errors")]
    public async Task<ActionResult> GetEmailErrors() => Ok(await _emailGateway.GetErrorMetricsAsync());

    [HttpGet("email/metrics/monthly")]
    public async Task<ActionResult> GetEmailMonthlyStats() => Ok(await _emailGateway.GetMonthlyStatisticsAsync());

    // Database logging endpoints
    [HttpGet("logs/{messageId}")]
    public async Task<ActionResult> GetNotificationLog(string messageId)
    {
        var log = await _loggingService.GetNotificationLogAsync(messageId);
        if (log == null)
            return NotFound();
        return Ok(log);
    }

    [HttpGet("logs/history/{recipient}")]
    public async Task<ActionResult> GetNotificationHistory(string recipient, [FromQuery] int skip = 0, [FromQuery] int take = 50)
    {
        var history = await _loggingService.GetNotificationHistoryAsync(recipient, skip, take);
        return Ok(history);
    }

    [HttpGet("metrics")]
    public async Task<ActionResult> GetMetrics([FromQuery] NotificationType? type = null, [FromQuery] string? provider = null, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        var metrics = await _loggingService.GetMetricsAsync(type, provider, from, to);
        return Ok(metrics);
    }

    [HttpGet("errors")]
    public async Task<ActionResult> GetErrors([FromQuery] NotificationType? type = null, [FromQuery] string? provider = null, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null, [FromQuery] bool unresolved = false)
    {
        var errors = await _loggingService.GetErrorsAsync(type, provider, from, to, unresolved);
        return Ok(errors);
    }
}