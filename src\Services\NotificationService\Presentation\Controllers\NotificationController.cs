using Microsoft.AspNetCore.Mvc;
using NotificationContract.Models;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Services;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NotificationsController : ControllerBase
{
    private readonly PluginCore.Services.NotificationService _notificationService;
    private readonly IPluginManager _pluginManager;

    public NotificationsController(
        PluginCore.Services.NotificationService notificationService,
        IPluginManager pluginManager)
    {
        _notificationService = notificationService;
        _pluginManager = pluginManager;
    }

    [HttpPost("send")]
    public async Task<ActionResult> Send(SendNotificationRequest request)
    {
        var results = new List<object>();

        // Send email if requested
        if (request.NotificationTypes.Contains(NotificationContract.Enums.NotificationType.Email))
        {
            var emailRequest = new EmailRequest(
                To: request.ReceptorMail,
                Subject: request.Subject ?? "Notification",
                Body: request.Body
            );

            var emailResult = await _notificationService.SendEmailAsync(emailRequest);
            results.Add(new { Type = "Email", Success = emailResult.IsSuccess, Error = emailResult.ErrorMessage });
        }

        // Send SMS if requested
        if (request.NotificationTypes.Contains(NotificationContract.Enums.NotificationType.Sms))
        {
            var smsRequest = new SmsRequest(
                PhoneNumber: request.ReceptorPhoneNumber,
                Message: request.Body
            );

            var smsResult = await _notificationService.SendSmsAsync(smsRequest);
            results.Add(new { Type = "SMS", Success = smsResult.IsSuccess, Error = smsResult.ErrorMessage });
        }

        // Send push notification if requested
        if (request.NotificationTypes.Contains(NotificationContract.Enums.NotificationType.PushMessage))
        {
            var pushRequest = new PushNotificationRequest(
                DeviceToken: request.DeviceToken,
                Title: request.Subject ?? "Notification",
                Body: request.Body
            );

            var pushResult = await _notificationService.SendPushNotificationAsync(pushRequest);
            results.Add(new { Type = "Push", Success = pushResult.IsSuccess, Error = pushResult.ErrorMessage });
        }

        return Ok(new { Results = results });
    }

    [HttpGet("plugins")]
    public async Task<ActionResult> GetPlugins()
    {
        var plugins = _pluginManager.GetAllPlugins()
            .Select(p => new
            {
                p.PluginInfo.Name,
                p.PluginInfo.Version,
                p.PluginInfo.Description,
                p.PluginInfo.Type,
                p.PluginInfo.Author,
                p.PluginInfo.IsEnabled
            });

        return Ok(plugins);
    }

    [HttpGet("plugins/health")]
    public async Task<ActionResult> GetPluginHealth()
    {
        var healthStatus = await _pluginManager.GetPluginHealthStatusAsync();
        return Ok(healthStatus);
    }

    [HttpPost("plugins/{pluginName}/toggle")]
    public async Task<ActionResult> TogglePlugin(string pluginName, [FromBody] bool enabled)
    {
        await _pluginManager.SetPluginEnabledAsync(pluginName, enabled);
        return Ok(new { Message = $"Plugin {pluginName} {(enabled ? "enabled" : "disabled")}" });
    }
}