using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotificationService.Data.Entities;
using NotificationService.Gateways.Interfaces;
using NotificationService.Services;
using System.Diagnostics;
using EmailContract.Models;
using SmsContract.Models;
using PushNotificationContract.Models;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NotificationsController : ControllerBase
{
    private readonly IEmailGateway _emailGateway;
    private readonly ISmsGateway _smsGateway;
    private readonly IPushGateway _pushGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly ILogger<NotificationsController> _logger;

    public NotificationsController(
        IEmailGateway emailGateway,
        ISmsGateway smsGateway,
        IPushGateway pushGateway,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        ILogger<NotificationsController> logger)
    {
        _emailGateway = emailGateway;
        _smsGateway = smsGateway;
        _pushGateway = pushGateway;
        _loggingService = loggingService;
        _queueService = queueService;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<ActionResult> Send(SendNotificationRequest request)
    {
        var results = new List<object>();
        var correlationId = Guid.NewGuid().ToString();

        try
        {
            // Queue email if requested
            if (request.NotificationTypes.Contains(NotificationType.Email))
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Email,
                    request.ReceptorMail,
                    request.Subject ?? "Notification",
                    request.Body,
                    request.UserId,
                    correlationId);

                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.Email,
                    Recipient = request.ReceptorMail,
                    Subject = request.Subject ?? "Notification",
                    Content = request.Body,
                    UserId = request.UserId,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { Type = "Email", Queued = true, MessageId = messageId, QueueId = queueId });
                _logger.LogInformation("Queued email notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            }

            // Queue SMS if requested
            if (request.NotificationTypes.Contains(NotificationType.Sms))
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Sms,
                    request.ReceptorPhoneNumber,
                    "",
                    request.Body,
                    request.UserId,
                    correlationId);

                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.Sms,
                    Recipient = request.ReceptorPhoneNumber,
                    Subject = "",
                    Content = request.Body,
                    UserId = request.UserId,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { Type = "SMS", Queued = true, MessageId = messageId, QueueId = queueId });
                _logger.LogInformation("Queued SMS notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            }

            // Queue push notification if requested
            if (request.NotificationTypes.Contains(NotificationType.PushMessage))
            {
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.PushMessage,
                    request.DeviceToken,
                    request.Subject ?? "Notification",
                    request.Body,
                    request.UserId,
                    correlationId);

                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.PushMessage,
                    Recipient = request.DeviceToken,
                    Subject = request.Subject ?? "Notification",
                    Content = request.Body,
                    UserId = request.UserId,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { Type = "Push", Queued = true, MessageId = messageId, QueueId = queueId });
                _logger.LogInformation("Queued push notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            }

            return Ok(new { Results = results, CorrelationId = correlationId, QueueLength = await _queueService.GetQueueLengthAsync() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing notification request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    // Email endpoints
    [HttpPost("email/send")]
    public async Task<ActionResult> SendEmail([FromBody] SendEmailRequest message)
    {
        var result = await _emailGateway.SendEmailAsync(message);
        return Ok(result);
    }

    [HttpGet("email/status")]
    public async Task<ActionResult> GetEmailStatus([FromQuery] string messageId)
    {
        var status = await _emailGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("email/history")]
    public async Task<ActionResult> GetEmailHistory([FromQuery] string email)
    {
        var history = await _emailGateway.GetMessageHistoryAsync(email);
        return Ok(history);
    }

    [HttpPost("email/resend")]
    public async Task<ActionResult> ResendEmail([FromQuery] string messageId)
    {
        var result = await _emailGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    // SMS endpoints
    [HttpPost("sms/send")]
    public async Task<ActionResult> SendSms([FromBody] SmsMessageRequest message)
    {
        var result = await _smsGateway.SendSmsAsync(message);
        return Ok(result);
    }

    [HttpPost("sms/send/bulk")]
    public async Task<ActionResult> SendBulkSms([FromBody] BulkSmsRequest bulk)
    {
        var result = await _smsGateway.SendBulkSmsAsync(bulk);
        return Ok(result);
    }

    [HttpGet("sms/status")]
    public async Task<ActionResult> GetSmsStatus([FromQuery] string messageId)
    {
        var status = await _smsGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("sms/history")]
    public async Task<ActionResult> GetSmsHistory([FromQuery] string phoneNumber)
    {
        var history = await _smsGateway.GetMessageHistoryAsync(phoneNumber);
        return Ok(history);
    }

    [HttpPost("sms/resend")]
    public async Task<ActionResult> ResendSms([FromQuery] string messageId)
    {
        var result = await _smsGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    // Push notification endpoints
    [HttpPost("push/send")]
    public async Task<ActionResult> SendPush([FromBody] PushMessageRequest message)
    {
        var result = await _pushGateway.SendPushAsync(message);
        return Ok(result);
    }

    [HttpPost("push/send/bulk")]
    public async Task<ActionResult> SendBulkPush([FromBody] BulkPushRequest bulk)
    {
        var result = await _pushGateway.SendBulkPushAsync(bulk);
        return Ok(result);
    }

    [HttpGet("push/status")]
    public async Task<ActionResult> GetPushStatus([FromQuery] string messageId)
    {
        var status = await _pushGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("push/history")]
    public async Task<ActionResult> GetPushHistory([FromQuery] string deviceToken)
    {
        var history = await _pushGateway.GetMessageHistoryAsync(deviceToken);
        return Ok(history);
    }

    [HttpPost("push/resend")]
    public async Task<ActionResult> ResendPush([FromQuery] string messageId)
    {
        var result = await _pushGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    // Admin endpoints
    [HttpGet("email/admin/providers")]
    public ActionResult GetEmailProviders() => Ok(_emailGateway.GetAvailableProviders());

    [HttpPost("email/admin/switch")]
    public ActionResult SwitchEmailProvider([FromQuery] string providerKey)
    {
        _emailGateway.SwitchProvider(providerKey);
        return Ok(new { provider = providerKey });
    }

    [HttpPost("email/admin/test")]
    public async Task<ActionResult> TestEmail([FromQuery] string to)
    {
        var result = await _emailGateway.SendTestMessageAsync(to);
        return Ok(result);
    }

    [HttpPost("email/admin/reload")]
    public ActionResult ReloadEmailProviders()
    {
        _emailGateway.ReloadProviders();
        return Ok();
    }

    [HttpPost("email/admin/config/update")]
    public ActionResult UpdateEmailConfig([FromQuery] string providerKey, [FromBody] Dictionary<string, string> config)
    {
        _emailGateway.UpdateProviderConfiguration(providerKey, config);
        return Ok();
    }

    // Metrics endpoints
    [HttpGet("email/metrics/summary")]
    public async Task<ActionResult> GetEmailSummary() => Ok(await _emailGateway.GetSummaryMetricsAsync());

    [HttpGet("email/metrics/detailed")]
    public async Task<ActionResult> GetEmailDetailed([FromQuery] DateTime? from, [FromQuery] DateTime? to)
    {
        return Ok(await _emailGateway.GetDetailedMetricsAsync(from, to));
    }

    [HttpGet("email/metrics/errors")]
    public async Task<ActionResult> GetEmailErrors() => Ok(await _emailGateway.GetErrorMetricsAsync());

    [HttpGet("email/metrics/monthly")]
    public async Task<ActionResult> GetEmailMonthlyStats() => Ok(await _emailGateway.GetMonthlyStatisticsAsync());

    // Database logging endpoints
    [HttpGet("logs/{messageId}")]
    public async Task<ActionResult> GetNotificationLog(string messageId)
    {
        var log = await _loggingService.GetNotificationLogAsync(messageId);
        if (log == null)
            return NotFound();
        return Ok(log);
    }

    [HttpGet("logs/history/{recipient}")]
    public async Task<ActionResult> GetNotificationHistory(string recipient, [FromQuery] int skip = 0, [FromQuery] int take = 50)
    {
        var history = await _loggingService.GetNotificationHistoryAsync(recipient, skip, take);
        return Ok(history);
    }

    [HttpGet("metrics")]
    public async Task<ActionResult> GetMetrics([FromQuery] NotificationType? type = null, [FromQuery] string? provider = null, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        var metrics = await _loggingService.GetMetricsAsync(type, provider, from, to);
        return Ok(metrics);
    }

    [HttpGet("errors")]
    public async Task<ActionResult> GetErrors([FromQuery] NotificationType? type = null, [FromQuery] string? provider = null, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null, [FromQuery] bool unresolved = false)
    {
        var errors = await _loggingService.GetErrorsAsync(type, provider, from, to, unresolved);
        return Ok(errors);
    }

    // Queue Management Endpoints
    [HttpGet("queue/status")]
    public async Task<ActionResult> GetQueueStatus()
    {
        try
        {
            var queueLength = await _queueService.GetQueueLengthAsync();
            var processingCount = await _queueService.GetProcessingCountAsync();
            var failedCount = await _queueService.GetFailedCountAsync();

            return Ok(new
            {
                QueueLength = queueLength,
                ProcessingCount = processingCount,
                FailedCount = failedCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting queue status");
            return StatusCode(500, new { Error = "Failed to get queue status", Message = ex.Message });
        }
    }

    [HttpPost("queue/retry-failed")]
    public async Task<ActionResult> RetryFailedNotifications()
    {
        try
        {
            var retriedCount = await _queueService.RetryFailedNotificationsAsync();
            return Ok(new { RetriedCount = retriedCount, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying failed notifications");
            return StatusCode(500, new { Error = "Failed to retry notifications", Message = ex.Message });
        }
    }

    [HttpDelete("queue/clear-failed")]
    public async Task<ActionResult> ClearFailedNotifications()
    {
        try
        {
            var clearedCount = await _queueService.ClearFailedNotificationsAsync();
            return Ok(new { ClearedCount = clearedCount, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing failed notifications");
            return StatusCode(500, new { Error = "Failed to clear failed notifications", Message = ex.Message });
        }
    }
}