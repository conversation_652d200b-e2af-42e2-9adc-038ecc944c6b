using NotificationContract.Models;

namespace NotificationService.Gateways.Interfaces;

public interface IEmailGateway
{
    // Core email operations
    Task<EmailResponse> SendEmailAsync(EmailMessageRequest message);
    Task<BulkEmailResponse> SendBulkEmailAsync(BulkEmailRequest bulk);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string email);
    Task<EmailResponse> ResendMessageAsync(string messageId);

    // Admin operations
    IEnumerable<ProviderInfo> GetAvailableProviders();
    void SwitchProvider(string providerKey);
    Task<EmailResponse> SendTestMessageAsync(string to);
    void ReloadProviders();
    void UpdateProviderConfiguration(string providerKey, Dictionary<string, string> config);

    // Metrics operations
    Task<SummaryMetricsResponse> GetSummaryMetricsAsync();
    Task<DetailedMetricsResponse> GetDetailedMetricsAsync(DateTime? from, DateTime? to);
    Task<ErrorMetricsResponse> GetErrorMetricsAsync();
    Task<MonthlyStatisticsResponse> GetMonthlyStatisticsAsync();
}

public class ProviderInfo
{
    public string Key { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public bool IsEnabled { get; set; }
    public Dictionary<string, object> Configuration { get; set; } = new();
}

public class SummaryMetricsResponse
{
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalFailed { get; set; }
    public double SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public DateTime LastUpdated { get; set; }
}

public class DetailedMetricsResponse
{
    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public List<DailyMetrics> DailyMetrics { get; set; } = new();
    public List<ProviderMetrics> ProviderMetrics { get; set; } = new();
}

public class DailyMetrics
{
    public DateTime Date { get; set; }
    public int Sent { get; set; }
    public int Delivered { get; set; }
    public int Failed { get; set; }
    public double SuccessRate { get; set; }
}

public class ProviderMetrics
{
    public string Provider { get; set; } = string.Empty;
    public int Sent { get; set; }
    public int Delivered { get; set; }
    public int Failed { get; set; }
    public double SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
}

public class ErrorMetricsResponse
{
    public int TotalErrors { get; set; }
    public List<ErrorSummary> ErrorsByType { get; set; } = new();
    public List<ErrorSummary> ErrorsByProvider { get; set; } = new();
    public List<RecentError> RecentErrors { get; set; } = new();
}

public class ErrorSummary
{
    public string Category { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Percentage { get; set; }
}

public class RecentError
{
    public DateTime OccurredAt { get; set; }
    public string Provider { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string? Recipient { get; set; }
}

public class MonthlyStatisticsResponse
{
    public int Year { get; set; }
    public int Month { get; set; }
    public int TotalSent { get; set; }
    public int TotalDelivered { get; set; }
    public int TotalFailed { get; set; }
    public double SuccessRate { get; set; }
    public List<DailyMetrics> DailyBreakdown { get; set; } = new();
    public List<ProviderMetrics> ProviderBreakdown { get; set; } = new();
}

public class MessageStatusResponse
{
    public string MessageId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? ErrorMessage { get; set; }
    public int RetryCount { get; set; }
    public string Provider { get; set; } = string.Empty;
}

public class MessageHistoryResponse
{
    public string Recipient { get; set; } = string.Empty;
    public int TotalMessages { get; set; }
    public List<MessageHistoryItem> Messages { get; set; } = new();
}

public class MessageHistoryItem
{
    public string MessageId { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? SentAt { get; set; }
    public string Provider { get; set; } = string.Empty;
}
