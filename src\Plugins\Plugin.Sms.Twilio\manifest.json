{"name": "<PERSON><PERSON><PERSON> Plugin", "version": "1.0.0", "description": "SMS plugin for Twilio provider", "author": "NotificationService Team", "type": "Sms", "provider": "<PERSON><PERSON><PERSON>", "assemblyName": "Plugin.Sms.Twilio.dll", "entryPoint": "Plugin.Sms.Twilio.TwilioPlugin", "dependencies": [{"name": "<PERSON><PERSON><PERSON>", "version": "7.6.0", "isRequired": true}], "configuration": {"accountSid": {"type": "string", "description": "<PERSON><PERSON><PERSON> Account SID", "isRequired": true, "isSecret": false}, "authToken": {"type": "string", "description": "<PERSON><PERSON><PERSON>", "isRequired": true, "isSecret": true}, "from": {"type": "string", "description": "Default sender phone number", "isRequired": true, "isSecret": false}, "messagingServiceSid": {"type": "string", "description": "Twilio Messaging Service SID (optional)", "isRequired": false, "isSecret": false}}, "supportedFeatures": ["SendSms", "BulkSms", "MessageStatus", "MessageHistory", "ResendMessage"], "minimumFrameworkVersion": "net9.0", "isEnabled": true, "priority": 90, "metadata": {"website": "https://www.twilio.com", "documentation": "https://www.twilio.com/docs/sms"}}