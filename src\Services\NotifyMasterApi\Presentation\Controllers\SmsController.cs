using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotificationService.Data.Entities;
using NotificationService.Gateways.Interfaces;
using NotificationService.Services;
using SmsContract.Models;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SmsController : ControllerBase
{
    private readonly ISmsGateway _smsGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly ILogger<SmsController> _logger;

    public SmsController(
        ISmsGateway smsGateway,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        ILogger<SmsController> logger)
    {
        _smsGateway = smsGateway;
        _loggingService = loggingService;
        _queueService = queueService;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<ActionResult> SendSms([FromBody] SmsMessageRequest message)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();
            
            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.Sms,
                message.PhoneNumber,
                "",
                message.Message,
                message.UserId,
                correlationId);

            // Queue the notification
            var queueItem = new NotificationQueueItem
            {
                MessageId = messageId,
                Type = NotificationType.Sms,
                Recipient = message.PhoneNumber,
                Subject = "",
                Content = message.Message,
                UserId = message.UserId,
                CorrelationId = correlationId,
                Priority = NotificationPriority.Normal
            };

            var queueId = await _queueService.QueueNotificationAsync(queueItem);
            
            _logger.LogInformation("Queued SMS notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            
            return Ok(new { 
                Success = true, 
                MessageId = messageId, 
                QueueId = queueId, 
                CorrelationId = correlationId 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing SMS request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    [HttpPost("send/direct")]
    public async Task<ActionResult> SendSmsDirect([FromBody] SmsMessageRequest message)
    {
        var result = await _smsGateway.SendSmsAsync(message);
        return Ok(result);
    }

    [HttpPost("send/bulk")]
    public async Task<ActionResult> SendBulkSms([FromBody] BulkSmsRequest bulk)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();
            var results = new List<object>();

            foreach (var sms in bulk.Messages)
            {
                // Log each SMS
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.Sms,
                    sms.PhoneNumber,
                    "",
                    sms.Message,
                    sms.UserId,
                    correlationId);

                // Queue each SMS
                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.Sms,
                    Recipient = sms.PhoneNumber,
                    Subject = "",
                    Content = sms.Message,
                    UserId = sms.UserId,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { MessageId = messageId, QueueId = queueId, PhoneNumber = sms.PhoneNumber });
            }

            _logger.LogInformation("Queued {Count} SMS notifications with correlation ID {CorrelationId}", bulk.Messages.Count, correlationId);
            
            return Ok(new { 
                Success = true, 
                Results = results, 
                CorrelationId = correlationId,
                TotalCount = bulk.Messages.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing bulk SMS request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    [HttpPost("send/bulk/direct")]
    public async Task<ActionResult> SendBulkSmsDirect([FromBody] BulkSmsRequest bulk)
    {
        var result = await _smsGateway.SendBulkSmsAsync(bulk);
        return Ok(result);
    }

    [HttpGet("status")]
    public async Task<ActionResult> GetSmsStatus([FromQuery] string messageId)
    {
        var status = await _smsGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("history")]
    public async Task<ActionResult> GetSmsHistory([FromQuery] string phoneNumber)
    {
        var history = await _smsGateway.GetMessageHistoryAsync(phoneNumber);
        return Ok(history);
    }

    [HttpPost("resend")]
    public async Task<ActionResult> ResendSms([FromQuery] string messageId)
    {
        var result = await _smsGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    [HttpGet("providers")]
    public ActionResult GetSmsProviders() => Ok(_smsGateway.GetAvailableProviders());

    [HttpPost("switch-provider")]
    public ActionResult SwitchSmsProvider([FromQuery] string providerKey)
    {
        _smsGateway.SwitchProvider(providerKey);
        return Ok(new { provider = providerKey });
    }

    [HttpPost("test")]
    public async Task<ActionResult> TestSms([FromQuery] string phoneNumber)
    {
        var result = await _smsGateway.SendTestMessageAsync(phoneNumber);
        return Ok(result);
    }

    [HttpPost("reload-providers")]
    public ActionResult ReloadSmsProviders()
    {
        _smsGateway.ReloadProviders();
        return Ok();
    }

    [HttpPost("config/update")]
    public ActionResult UpdateSmsConfig([FromQuery] string providerKey, [FromBody] Dictionary<string, string> config)
    {
        _smsGateway.UpdateProviderConfiguration(providerKey, config);
        return Ok();
    }

    [HttpGet("metrics/summary")]
    public async Task<ActionResult> GetSmsSummary() => Ok(await _smsGateway.GetSummaryMetricsAsync());

    [HttpGet("metrics/detailed")]
    public async Task<ActionResult> GetSmsDetailed([FromQuery] DateTime? from, [FromQuery] DateTime? to)
    {
        return Ok(await _smsGateway.GetDetailedMetricsAsync(from, to));
    }

    [HttpGet("metrics/errors")]
    public async Task<ActionResult> GetSmsErrors() => Ok(await _smsGateway.GetErrorMetricsAsync());

    [HttpGet("metrics/monthly")]
    public async Task<ActionResult> GetSmsMonthlyStats() => Ok(await _smsGateway.GetMonthlyStatisticsAsync());
}
