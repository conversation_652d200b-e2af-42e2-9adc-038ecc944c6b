using Microsoft.AspNetCore.Mvc;
using PushNotificationService.Library.Interfaces;
using NotificationService.Services;
using NotificationContract.Enums;

namespace NotificationService.Controllers;

/// <summary>
/// Push notification service metrics and analytics controller
/// </summary>
[ApiController]
[Route("api/push/metrics")]
[Produces("application/json")]
[Tags("Push Metrics")]
public class PushMetricsController : ControllerBase
{
    private readonly IPushNotificationService _pushService;
    private readonly INotificationLoggingService _loggingService;
    private readonly ILogger<PushMetricsController> _logger;

    public PushMetricsController(
        IPushNotificationService pushService,
        INotificationLoggingService loggingService,
        ILogger<PushMetricsController> logger)
    {
        _pushService = pushService;
        _loggingService = loggingService;
        _logger = logger;
    }

    /// <summary>
    /// Get push notification service summary metrics
    /// </summary>
    /// <returns>Summary metrics including total sent, success rate, and recent activity</returns>
    /// <response code="200">Summary metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("summary")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetSummaryMetrics()
    {
        try
        {
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Push);
            var serviceMetrics = await _pushService.GetSummaryMetricsAsync();
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceMetrics,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification summary metrics");
            return StatusCode(500, new { Error = "Failed to get summary metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get detailed push notification metrics for a specific time period
    /// </summary>
    /// <param name="from">Start date for metrics (optional)</param>
    /// <param name="to">End date for metrics (optional)</param>
    /// <param name="platform">Filter by platform (iOS, Android, Web) (optional)</param>
    /// <returns>Detailed metrics including hourly/daily breakdowns</returns>
    /// <response code="200">Detailed metrics retrieved successfully</response>
    /// <response code="400">Invalid date range</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("detailed")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDetailedMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? platform = null)
    {
        try
        {
            if (from.HasValue && to.HasValue && from > to)
            {
                return BadRequest(new { Error = "Start date cannot be after end date" });
            }

            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Push, platform, from, to);
            var serviceMetrics = await _pushService.GetDetailedMetricsAsync(from, to);
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceMetrics,
                Period = new { From = from, To = to },
                Platform = platform,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed push notification metrics");
            return StatusCode(500, new { Error = "Failed to get detailed metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get push notification error metrics and failure analysis
    /// </summary>
    /// <param name="from">Start date for error analysis (optional)</param>
    /// <param name="to">End date for error analysis (optional)</param>
    /// <param name="platform">Filter by platform (iOS, Android, Web) (optional)</param>
    /// <param name="unresolved">Show only unresolved errors</param>
    /// <returns>Error metrics and failure patterns</returns>
    /// <response code="200">Error metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("errors")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetErrorMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? platform = null,
        [FromQuery] bool unresolved = false)
    {
        try
        {
            var errors = await _loggingService.GetErrorsAsync(NotificationType.Push, platform, from, to, unresolved);
            var serviceErrors = await _pushService.GetErrorMetricsAsync();
            
            return Ok(new
            {
                Database = errors,
                Service = serviceErrors,
                Period = new { From = from, To = to },
                Platform = platform,
                UnresolvedOnly = unresolved,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification error metrics");
            return StatusCode(500, new { Error = "Failed to get error metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get monthly push notification statistics and trends
    /// </summary>
    /// <param name="months">Number of months to include (default: 12)</param>
    /// <param name="platform">Filter by platform (iOS, Android, Web) (optional)</param>
    /// <returns>Monthly statistics and trend analysis</returns>
    /// <response code="200">Monthly statistics retrieved successfully</response>
    /// <response code="400">Invalid months parameter</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("monthly")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetMonthlyStatistics(
        [FromQuery] int months = 12,
        [FromQuery] string? platform = null)
    {
        try
        {
            if (months <= 0 || months > 24)
            {
                return BadRequest(new { Error = "Months must be between 1 and 24" });
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMonths(-months);
            
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Push, platform, startDate, endDate);
            var serviceStats = await _pushService.GetMonthlyStatisticsAsync();
            
            return Ok(new
            {
                Database = metrics,
                Service = serviceStats,
                Period = new { StartDate = startDate, EndDate = endDate, Months = months },
                Platform = platform,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monthly push notification statistics");
            return StatusCode(500, new { Error = "Failed to get monthly statistics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get real-time push notification service performance metrics
    /// </summary>
    /// <returns>Real-time performance data including response times and throughput</returns>
    /// <response code="200">Performance metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("performance")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetPerformanceMetrics()
    {
        try
        {
            // Get recent performance data (last hour)
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-1);
            
            var recentMetrics = await _loggingService.GetMetricsAsync(NotificationType.Push, null, startTime, endTime);
            
            return Ok(new
            {
                RecentActivity = recentMetrics,
                Period = new { StartTime = startTime, EndTime = endTime },
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification performance metrics");
            return StatusCode(500, new { Error = "Failed to get performance metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get push notification delivery rate analysis by platform
    /// </summary>
    /// <param name="days">Number of days to analyze (default: 30)</param>
    /// <returns>Delivery rate comparison across platforms</returns>
    /// <response code="200">Delivery rate analysis retrieved successfully</response>
    /// <response code="400">Invalid days parameter</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("delivery-rates")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDeliveryRateAnalysis([FromQuery] int days = 30)
    {
        try
        {
            if (days <= 0 || days > 365)
            {
                return BadRequest(new { Error = "Days must be between 1 and 365" });
            }

            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-days);
            
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Push, null, startDate, endDate);
            
            return Ok(new
            {
                DeliveryAnalysis = metrics,
                Period = new { StartDate = startDate, EndDate = endDate, Days = days },
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification delivery rate analysis");
            return StatusCode(500, new { Error = "Failed to get delivery rate analysis", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get push notification engagement metrics
    /// </summary>
    /// <param name="from">Start date for engagement analysis (optional)</param>
    /// <param name="to">End date for engagement analysis (optional)</param>
    /// <param name="platform">Filter by platform (iOS, Android, Web) (optional)</param>
    /// <returns>Engagement metrics including open rates and click-through rates</returns>
    /// <response code="200">Engagement metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("engagement")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetEngagementMetrics(
        [FromQuery] DateTime? from = null,
        [FromQuery] DateTime? to = null,
        [FromQuery] string? platform = null)
    {
        try
        {
            var metrics = await _loggingService.GetMetricsAsync(NotificationType.Push, platform, from, to);
            
            return Ok(new
            {
                EngagementAnalysis = metrics,
                Period = new { From = from, To = to },
                Platform = platform,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification engagement metrics");
            return StatusCode(500, new { Error = "Failed to get engagement metrics", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get device token health metrics
    /// </summary>
    /// <param name="platform">Filter by platform (iOS, Android, Web) (optional)</param>
    /// <returns>Device token validity and health information</returns>
    /// <response code="200">Device token metrics retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("device-tokens")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetDeviceTokenMetrics([FromQuery] string? platform = null)
    {
        try
        {
            var serviceMetrics = await _pushService.GetDeviceTokenMetricsAsync();
            
            return Ok(new
            {
                DeviceTokenHealth = serviceMetrics,
                Platform = platform,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device token metrics");
            return StatusCode(500, new { Error = "Failed to get device token metrics", Message = ex.Message });
        }
    }
}
