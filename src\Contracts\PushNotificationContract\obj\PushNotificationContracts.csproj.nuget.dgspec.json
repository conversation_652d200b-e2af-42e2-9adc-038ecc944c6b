{"format": 1, "restore": {"/Users/<USER>/Projects/NotificationService/src/Contracts/PushNotificationContracts/PushNotificationContracts.csproj": {}}, "projects": {"/Users/<USER>/Projects/NotificationService/src/Contracts/PushNotificationContracts/PushNotificationContracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/NotificationService/src/Contracts/PushNotificationContracts/PushNotificationContracts.csproj", "projectName": "PushNotificationContracts", "projectPath": "/Users/<USER>/Projects/NotificationService/src/Contracts/PushNotificationContracts/PushNotificationContracts.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/NotificationService/src/Contracts/PushNotificationContracts/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.101/RuntimeIdentifierGraph.json"}}}}}