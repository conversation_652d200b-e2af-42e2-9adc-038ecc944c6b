# NotificationService Plugin Architecture

## Overview

The NotificationService has been refactored from a microservices architecture to a plugin-based architecture. This allows for better modularity, easier maintenance, and dynamic loading of notification providers.

## Architecture Components

### Core Components

1. **PluginContract** (`src/Contracts/PluginContract/`)
   - Contains all plugin interfaces and models
   - Defines the contract that all plugins must implement
   - Includes base interfaces: `INotificationPlugin`, `IEmailPlugin`, `ISmsPlugin`, `IPushNotificationPlugin`

2. **PluginCore** (`src/Core/PluginCore/`)
   - Contains the plugin management system
   - Includes `PluginManager` for discovering and managing plugins
   - Provides `NotificationService` that orchestrates plugin calls
   - Contains base classes for plugin development

### Plugin Projects

1. **Kavenegar SMS Plugin** (`src/Plugins/NotificationService.Plugins.Kavenegar/`)
   - Implements SMS functionality using Kavenegar API
   - Supports configuration validation and health checks

2. **SMTP Email Plugin** (`src/Plugins/NotificationService.Plugins.Smtp/`)
   - Implements email functionality using SMTP
   - Supports HTML/text emails, attachments, CC/BCC

3. **Firebase Push Plugin** (`src/Plugins/NotificationService.Plugins.Firebase/`)
   - Implements push notifications using Firebase Cloud Messaging
   - Supports custom data and dry-run mode

## Plugin Development

### Creating a New Plugin

1. Create a new project in `src/Plugins/`
2. Reference `PluginContract` and `PluginCore`
3. Implement the appropriate plugin interface (`IEmailPlugin`, `ISmsPlugin`, or `IPushNotificationPlugin`)
4. Add the `[NotificationPlugin]` attribute to your plugin class
5. Inherit from `BaseNotificationPlugin` for common functionality

### Plugin Interface

```csharp
[NotificationPlugin("PluginName", "1.0.0", "Description", PluginType.Email, "Author")]
public class MyPlugin : BaseNotificationPlugin, IEmailPlugin
{
    public override PluginInfo PluginInfo => new(
        "PluginName", "1.0.0", "Description", 
        PluginType.Email, "Author", true);

    public override async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        // Validate plugin configuration
        return true;
    }

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);
        // Initialize plugin resources
    }

    public async Task<NotificationResponse> SendEmailAsync(EmailRequest request, CancellationToken cancellationToken = default)
    {
        // Implement email sending logic
        return new EmailResponse(true);
    }

    public override async Task<bool> HealthCheckAsync()
    {
        // Implement health check logic
        return true;
    }
}
```

## Configuration

### Plugin Configuration Structure

```json
{
  "Plugins": {
    "PluginsDirectory": "plugins",
    "PluginName": {
      "IsEnabled": true,
      "ConfigProperty1": "value1",
      "ConfigProperty2": "value2"
    }
  }
}
```

### Example Configurations

#### Kavenegar SMS Plugin
```json
{
  "Plugins": {
    "Kavenegar": {
      "ApiKey": "your-api-key",
      "SenderNumber": "your-sender-number",
      "IsEnabled": true,
      "TimeoutSeconds": 30,
      "RetryCount": 3
    }
  }
}
```

#### SMTP Email Plugin
```json
{
  "Plugins": {
    "SMTP": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "UseSsl": true,
      "Username": "<EMAIL>",
      "Password": "your-app-password",
      "FromEmail": "<EMAIL>",
      "FromName": "Notification Service",
      "IsEnabled": true,
      "TimeoutSeconds": 30
    }
  }
}
```

## API Endpoints

### Send Notifications
- **POST** `/api/notifications/send`
- Supports multiple notification types in a single request

### Plugin Management
- **GET** `/api/notifications/plugins` - List all plugins
- **GET** `/api/notifications/plugins/health` - Get plugin health status
- **POST** `/api/notifications/plugins/{pluginName}/toggle` - Enable/disable plugin

## Plugin Discovery

Plugins are automatically discovered at startup by:
1. Scanning the configured plugins directory
2. Loading assemblies and searching for classes with `[NotificationPlugin]` attribute
3. Validating plugin configuration
4. Initializing enabled plugins

## Benefits

1. **Modularity**: Each notification provider is a separate project
2. **Extensibility**: Easy to add new notification providers
3. **Configuration**: Runtime plugin enable/disable
4. **Health Monitoring**: Built-in health checks for all plugins
5. **Fallback Support**: Automatic fallback to other plugins if one fails
6. **Hot Swapping**: Plugins can be updated without restarting the service

## Migration from Microservices

The original microservices (EmailService, SmsService, PushNotificationService) can still be used alongside the plugin system, or completely replaced by deploying plugins to the NotificationService.
