<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
        <PackageReference Include="System.Text.Json" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Contracts\NotificationContract\NotificationContract.csproj" />
        <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="manifest.json" />
    </ItemGroup>

</Project>
