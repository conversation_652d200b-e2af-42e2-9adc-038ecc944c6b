using NotificationContract.Models;

namespace PluginCore.Interfaces;

public interface IEmailPlugin : IPlugin
{
    Task<EmailResponse> SendAsync(EmailMessageRequest request);
    Task<BulkEmailResponse> SendBulkAsync(BulkEmailRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string emailAddress);
    Task<EmailResponse> ResendMessageAsync(string messageId);
    Task<bool> ValidateConfigurationAsync();
}
