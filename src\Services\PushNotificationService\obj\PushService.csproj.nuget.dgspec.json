{"format": 1, "restore": {"/Users/<USER>/Projects/NotificationService/src/Services/PushService/PushService.csproj": {}}, "projects": {"/Users/<USER>/Projects/NotificationService/src/Services/PushService/PushService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/NotificationService/src/Services/PushService/PushService.csproj", "projectName": "PushService", "projectPath": "/Users/<USER>/Projects/NotificationService/src/Services/PushService/PushService.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/NotificationService/src/Services/PushService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"MassTransit": {"target": "Package", "version": "[8.1.1-develop.1522, )"}, "MassTransit.AspNetCore": {"target": "Package", "version": "[7.3.1, )"}, "MassTransit.RabbitMQ": {"target": "Package", "version": "[8.1.1-develop.1522, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[7.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/7.0.101/RuntimeIdentifierGraph.json"}}}}}