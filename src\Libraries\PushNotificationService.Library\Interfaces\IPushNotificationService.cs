using NotificationContract.Models;

namespace PushNotificationService.Library.Interfaces;

public interface IPushNotificationService
{
    Task<PushResponse> SendAsync(PushMessageRequest request);
    Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken);
    Task<PushResponse> ResendMessageAsync(string messageId);
}
