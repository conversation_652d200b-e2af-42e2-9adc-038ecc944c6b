using NotificationContract.Models;

namespace PluginCore.Interfaces;

public interface IPushPlugin : IPlugin
{
    Task<PushResponse> SendAsync(PushMessageRequest request);
    Task<BulkPushResponse> SendBulkAsync(BulkPushRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken);
    Task<PushResponse> ResendMessageAsync(string messageId);
    Task<bool> ValidateConfigurationAsync();
}
