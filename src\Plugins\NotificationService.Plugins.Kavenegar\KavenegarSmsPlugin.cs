using Kavenegar;
using Kavenegar.Core.Exceptions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NotificationService.Plugins.Kavenegar.Configuration;
using PluginContract.Attributes;
using PluginContract.Enums;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;

namespace NotificationService.Plugins.Kavenegar;

[NotificationPlugin("Kavenegar", "1.0.0", "Kavenegar SMS provider plugin", PluginType.Sms, "NotificationService Team")]
public sealed class KavenegarSmsPlugin : BaseNotificationPlugin, ISmsPlugin
{
    private KavenegarApi? _kavenegarApi;
    private KavenegarSettings? _settings;

    public override PluginInfo PluginInfo => new(
        "Kavenegar",
        "1.0.0",
        "Kavenegar SMS provider plugin for sending SMS messages",
        PluginType.Sms,
        "NotificationService Team",
        true
    );

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        var settings = configuration.GetSection("Plugins:Kavenegar").Get<KavenegarSettings>();
        if (settings != null)
        {
            services.Configure<KavenegarSettings>(configuration.GetSection("Plugins:Kavenegar"));
        }
    }

    public override async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        var settings = configuration.GetSection("Plugins:Kavenegar").Get<KavenegarSettings>();
        
        if (settings == null)
        {
            Logger?.LogError("Kavenegar settings not found in configuration");
            return false;
        }

        if (string.IsNullOrWhiteSpace(settings.ApiKey))
        {
            Logger?.LogError("Kavenegar API key is required");
            return false;
        }

        if (string.IsNullOrWhiteSpace(settings.SenderNumber))
        {
            Logger?.LogError("Kavenegar sender number is required");
            return false;
        }

        return true;
    }

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);
        
        _settings = GetConfiguration<KavenegarSettings>();
        if (_settings != null)
        {
            _kavenegarApi = new KavenegarApi(_settings.ApiKey);
            Logger?.LogInformation("Kavenegar SMS plugin initialized successfully");
        }
        else
        {
            throw new InvalidOperationException("Failed to load Kavenegar settings");
        }
    }

    public async Task<NotificationResponse> SendSmsAsync(SmsRequest request, CancellationToken cancellationToken = default)
    {
        if (_kavenegarApi == null || _settings == null)
        {
            return new SmsResponse(false, ErrorMessage: "Plugin not properly initialized");
        }

        try
        {
            Logger?.LogInformation("Sending SMS to {PhoneNumber} via Kavenegar", request.PhoneNumber);

            var result = await _kavenegarApi.Send(
                sender: _settings.SenderNumber,
                receptor: request.PhoneNumber,
                message: request.Message
            );

            Logger?.LogInformation("SMS sent successfully via Kavenegar. MessageId: {MessageId}", result.Messageid);

            return new SmsResponse(
                true,
                MessageId: result.Messageid.ToString(),
                ResponseData: new Dictionary<string, object>
                {
                    ["Status"] = result.Status,
                    ["StatusText"] = result.StatusText,
                    ["Cost"] = result.Cost,
                    ["Date"] = result.Date
                }
            );
        }
        catch (ApiException ex)
        {
            Logger?.LogError(ex, "Kavenegar API error: {Message}", ex.Message);
            return new SmsResponse(false, ErrorMessage: $"API Error: {ex.Message}");
        }
        catch (HttpException ex)
        {
            Logger?.LogError(ex, "Kavenegar HTTP error: {Message}", ex.Message);
            return new SmsResponse(false, ErrorMessage: $"HTTP Error: {ex.Message}");
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "Unexpected error sending SMS via Kavenegar");
            return new SmsResponse(false, ErrorMessage: $"Unexpected error: {ex.Message}");
        }
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        if (request is SmsRequest smsRequest)
        {
            return await SendSmsAsync(smsRequest, cancellationToken);
        }

        return new SmsResponse(false, ErrorMessage: "Invalid request type for SMS plugin");
    }

    public override async Task<bool> HealthCheckAsync()
    {
        if (_kavenegarApi == null || _settings == null)
        {
            return false;
        }

        try
        {
            // Perform a simple API call to check connectivity
            await _kavenegarApi.AccountInfo();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
