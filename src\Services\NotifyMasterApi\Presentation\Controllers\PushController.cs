using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationContract.Models;
using NotificationService.Data.Entities;
using NotificationService.Gateways.Interfaces;
using NotificationService.Services;
using PushNotificationContract.Models;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PushController : ControllerBase
{
    private readonly IPushGateway _pushGateway;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly ILogger<PushController> _logger;

    public PushController(
        IPushGateway pushGateway,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        ILogger<PushController> logger)
    {
        _pushGateway = pushGateway;
        _loggingService = loggingService;
        _queueService = queueService;
        _logger = logger;
    }

    [HttpPost("send")]
    public async Task<ActionResult> SendPush([FromBody] PushMessageRequest message)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();
            
            // Log the notification
            var messageId = await _loggingService.LogNotificationAsync(
                NotificationType.PushMessage,
                message.DeviceToken,
                message.Title,
                message.Body,
                message.UserId,
                correlationId);

            // Queue the notification
            var queueItem = new NotificationQueueItem
            {
                MessageId = messageId,
                Type = NotificationType.PushMessage,
                Recipient = message.DeviceToken,
                Subject = message.Title,
                Content = message.Body,
                UserId = message.UserId,
                CorrelationId = correlationId,
                Priority = NotificationPriority.Normal
            };

            var queueId = await _queueService.QueueNotificationAsync(queueItem);
            
            _logger.LogInformation("Queued push notification {MessageId} with queue ID {QueueId}", messageId, queueId);
            
            return Ok(new { 
                Success = true, 
                MessageId = messageId, 
                QueueId = queueId, 
                CorrelationId = correlationId 
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing push notification request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    [HttpPost("send/direct")]
    public async Task<ActionResult> SendPushDirect([FromBody] PushMessageRequest message)
    {
        var result = await _pushGateway.SendPushAsync(message);
        return Ok(result);
    }

    [HttpPost("send/bulk")]
    public async Task<ActionResult> SendBulkPush([FromBody] BulkPushRequest bulk)
    {
        try
        {
            var correlationId = Guid.NewGuid().ToString();
            var results = new List<object>();

            foreach (var push in bulk.Messages)
            {
                // Log each push notification
                var messageId = await _loggingService.LogNotificationAsync(
                    NotificationType.PushMessage,
                    push.DeviceToken,
                    push.Title,
                    push.Body,
                    push.UserId,
                    correlationId);

                // Queue each push notification
                var queueItem = new NotificationQueueItem
                {
                    MessageId = messageId,
                    Type = NotificationType.PushMessage,
                    Recipient = push.DeviceToken,
                    Subject = push.Title,
                    Content = push.Body,
                    UserId = push.UserId,
                    CorrelationId = correlationId,
                    Priority = NotificationPriority.Normal
                };

                var queueId = await _queueService.QueueNotificationAsync(queueItem);
                results.Add(new { MessageId = messageId, QueueId = queueId, DeviceToken = push.DeviceToken });
            }

            _logger.LogInformation("Queued {Count} push notifications with correlation ID {CorrelationId}", bulk.Messages.Count, correlationId);
            
            return Ok(new { 
                Success = true, 
                Results = results, 
                CorrelationId = correlationId,
                TotalCount = bulk.Messages.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing bulk push notification request");
            return StatusCode(500, new { Error = "Internal server error", Message = ex.Message });
        }
    }

    [HttpPost("send/bulk/direct")]
    public async Task<ActionResult> SendBulkPushDirect([FromBody] BulkPushRequest bulk)
    {
        var result = await _pushGateway.SendBulkPushAsync(bulk);
        return Ok(result);
    }

    [HttpGet("status")]
    public async Task<ActionResult> GetPushStatus([FromQuery] string messageId)
    {
        var status = await _pushGateway.GetMessageStatusAsync(messageId);
        return Ok(status);
    }

    [HttpGet("history")]
    public async Task<ActionResult> GetPushHistory([FromQuery] string deviceToken)
    {
        var history = await _pushGateway.GetMessageHistoryAsync(deviceToken);
        return Ok(history);
    }

    [HttpPost("resend")]
    public async Task<ActionResult> ResendPush([FromQuery] string messageId)
    {
        var result = await _pushGateway.ResendMessageAsync(messageId);
        return Ok(result);
    }

    [HttpGet("providers")]
    public ActionResult GetPushProviders() => Ok(_pushGateway.GetAvailableProviders());

    [HttpPost("switch-provider")]
    public ActionResult SwitchPushProvider([FromQuery] string providerKey)
    {
        _pushGateway.SwitchProvider(providerKey);
        return Ok(new { provider = providerKey });
    }

    [HttpPost("test")]
    public async Task<ActionResult> TestPush([FromQuery] string deviceToken)
    {
        var result = await _pushGateway.SendTestMessageAsync(deviceToken);
        return Ok(result);
    }

    [HttpPost("reload-providers")]
    public ActionResult ReloadPushProviders()
    {
        _pushGateway.ReloadProviders();
        return Ok();
    }

    [HttpPost("config/update")]
    public ActionResult UpdatePushConfig([FromQuery] string providerKey, [FromBody] Dictionary<string, string> config)
    {
        _pushGateway.UpdateProviderConfiguration(providerKey, config);
        return Ok();
    }

    [HttpGet("metrics/summary")]
    public async Task<ActionResult> GetPushSummary() => Ok(await _pushGateway.GetSummaryMetricsAsync());

    [HttpGet("metrics/detailed")]
    public async Task<ActionResult> GetPushDetailed([FromQuery] DateTime? from, [FromQuery] DateTime? to)
    {
        return Ok(await _pushGateway.GetDetailedMetricsAsync(from, to));
    }

    [HttpGet("metrics/errors")]
    public async Task<ActionResult> GetPushErrors() => Ok(await _pushGateway.GetErrorMetricsAsync());

    [HttpGet("metrics/monthly")]
    public async Task<ActionResult> GetPushMonthlyStats() => Ok(await _pushGateway.GetMonthlyStatisticsAsync());
}
