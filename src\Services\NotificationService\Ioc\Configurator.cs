using System.Reflection;
using System.Text.Json.Serialization;
using MassTransit;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using NotificationService.Data;
using NotificationService.Gateways;
using NotificationService.Gateways.Interfaces;
using NotificationService.Services;
using PluginCore.Extensions;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using StackExchange.Redis;

namespace NotificationService.Ioc;

public static class Configurator
{
    public static void InjectService(this IServiceCollection services , IConfiguration configuration)
    {
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen();
        services.AddMassTransit(configurator =>
        {
            configurator.UsingRabbitMq();
        });

        // Add Entity Framework with PostgreSQL
        services.AddDbContext<NotificationDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Add HTTP clients for gateway services
        services.AddHttpClient<IEmailGateway, EmailGatewayClient>(client =>
        {
            client.BaseAddress = new Uri(configuration["Services:EmailService:BaseUrl"] ?? "http://localhost:5001");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        services.AddHttpClient<ISmsGateway, SmsGatewayClient>(client =>
        {
            client.BaseAddress = new Uri(configuration["Services:SmsService:BaseUrl"] ?? "http://localhost:5002");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        services.AddHttpClient<IPushGateway, PushGatewayClient>(client =>
        {
            client.BaseAddress = new Uri(configuration["Services:PushService:BaseUrl"] ?? "http://localhost:5003");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        // Add notification logging service
        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();

        // Add Redis connection
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
            return ConnectionMultiplexer.Connect(connectionString);
        });

        // Add Redis queue services
        services.AddSingleton<INotificationQueueService, RedisNotificationQueueService>();
        services.AddHostedService<NotificationProcessingService>();

        // Add plugin system (keeping for backward compatibility)
        services.AddPluginSystem();

        Configuration.SetUp(configuration);
        AddLogging(configuration);
    }
    
    public static void ConfigurePipeline(this WebApplication app)
    {
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks("/health/ready", new HealthCheckOptions()
            {
                Predicate = (check) => check.Tags.Contains("ready"),
            });

            endpoints.MapHealthChecks("/health/live", new HealthCheckOptions());
        });
        app.UseHttpsRedirection();
        app.UseAuthorization();
        app.MapControllers();

    }
    
    private static void AddLogging(IConfiguration configuration)
    {
        string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithEnvironmentName()
            .Enrich.WithMachineName()
            .WriteTo.Debug()
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(Configuration.AppSetting.ElasticSearch.Uri))
            {
                AutoRegisterTemplate = true,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv6,
                IndexFormat = $"{Assembly.GetExecutingAssembly().GetName().Name!.ToLower().Replace(".", "-")}-{environment?.ToLower().Replace(".", "-")}-{DateTime.UtcNow:yyyy-MM}"
            })
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}