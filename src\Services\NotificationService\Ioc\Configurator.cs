using System.Reflection;
using System.Text.Json.Serialization;
using MassTransit;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using NotificationService.Data;
using NotificationService.Services;
using PluginCore.Extensions;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using StackExchange.Redis;
using EmailService.Library.Extensions;
using SmsService.Library.Extensions;
using PushNotificationService.Library.Extensions;

namespace NotificationService.Ioc;

public static class Configurator
{
    public static void InjectService(this IServiceCollection services , IConfiguration configuration)
    {
        services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
            });
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "NotificationService API",
                Version = "v1",
                Description = "A comprehensive notification service supporting Email, SMS, and Push notifications with plugin architecture",
                Contact = new OpenApiContact
                {
                    Name = "NotificationService Team",
                    Email = "<EMAIL>"
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                }
            });

            // Include XML comments for better documentation
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            if (File.Exists(xmlPath))
            {
                c.IncludeXmlComments(xmlPath);
            }

            // Add security definitions for future authentication
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            // Group endpoints by tags for better organization in Stoplight
            c.TagActionsBy(api => new[] { api.GroupName ?? api.ActionDescriptor.RouteValues["controller"] });
            c.DocInclusionPredicate((name, api) => true);
        });
        services.AddMassTransit(configurator =>
        {
            configurator.UsingRabbitMq();
        });

        // Add Entity Framework with PostgreSQL
        services.AddDbContext<NotificationDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // Add library services instead of HTTP gateways
        services.AddEmailService(configuration);
        services.AddSmsService();
        services.AddPushNotificationService();

        // Add notification logging service
        services.AddScoped<INotificationLoggingService, NotificationLoggingService>();

        // Add Redis connection
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "localhost:6379";
            return ConnectionMultiplexer.Connect(connectionString);
        });

        // Add Redis queue services
        services.AddSingleton<INotificationQueueService, RedisNotificationQueueService>();
        services.AddHostedService<NotificationProcessingService>();

        // Add plugin system (keeping for backward compatibility)
        services.AddPluginSystem();

        Configuration.SetUp(configuration);
        AddLogging(configuration);
    }
    
    public static void ConfigurePipeline(this WebApplication app)
    {
        // Always expose OpenAPI JSON for Stoplight integration
        app.UseSwagger(c =>
        {
            c.RouteTemplate = "api-docs/{documentName}/openapi.json";
        });

        // Add a custom endpoint to serve the OpenAPI spec at a well-known location
        app.MapGet("/openapi.json", async (HttpContext context) =>
        {
            context.Response.Redirect("/api-docs/v1/openapi.json");
        });

        // Add Stoplight Elements UI for development (optional)
        if (app.Environment.IsDevelopment())
        {
            app.MapGet("/docs", async (HttpContext context) =>
            {
                var html = """
                <!doctype html>
                <html lang="en">
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
                    <title>NotificationService API Documentation</title>
                    <script src="https://unpkg.com/@stoplight/elements/web-components.min.js"></script>
                    <link rel="stylesheet" href="https://unpkg.com/@stoplight/elements/styles.min.css">
                    <style>
                        body { margin: 0; padding: 0; }
                    </style>
                </head>
                <body>
                    <elements-api
                        apiDescriptionUrl="/openapi.json"
                        router="hash"
                        layout="sidebar"
                        hideInternal="false"
                    />
                </body>
                </html>
                """;

                context.Response.ContentType = "text/html";
                await context.Response.WriteAsync(html);
            });
        }

        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks("/health/ready", new HealthCheckOptions()
            {
                Predicate = (check) => check.Tags.Contains("ready"),
            });

            endpoints.MapHealthChecks("/health/live", new HealthCheckOptions());
        });
        app.UseHttpsRedirection();
        app.UseAuthorization();
        app.MapControllers();

    }
    
    private static void AddLogging(IConfiguration configuration)
    {
        string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithEnvironmentName()
            .Enrich.WithMachineName()
            .WriteTo.Debug()
            .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(Configuration.AppSetting.ElasticSearch.Uri))
            {
                AutoRegisterTemplate = true,
                AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv6,
                IndexFormat = $"{Assembly.GetExecutingAssembly().GetName().Name!.ToLower().Replace(".", "-")}-{environment?.ToLower().Replace(".", "-")}-{DateTime.UtcNow:yyyy-MM}"
            })
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}