using NotificationContract.Models;
using NotificationService.Gateways.Interfaces;
using System.Text;
using System.Text.Json;

namespace NotificationService.Gateways;

public class SmsGatewayClient : ISmsGateway
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SmsGatewayClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public SmsGatewayClient(HttpClient httpClient, ILogger<SmsGatewayClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<SmsResponse> SendSmsAsync(SmsMessageRequest message)
    {
        try
        {
            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/sms/send", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<SmsResponse>(responseContent, _jsonOptions) 
                       ?? new SmsResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("SMS service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new SmsResponse(false, $"SMS service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling SMS service");
            return new SmsResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public async Task<BulkSmsResponse> SendBulkSmsAsync(BulkSmsRequest bulk)
    {
        try
        {
            var json = JsonSerializer.Serialize(bulk, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/sms/send/bulk", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<BulkSmsResponse>(responseContent, _jsonOptions) 
                       ?? new BulkSmsResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Bulk SMS service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new BulkSmsResponse(false, $"Bulk SMS service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling bulk SMS service");
            return new BulkSmsResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/sms/status?messageId={messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MessageStatusResponse>(responseContent, _jsonOptions) 
                       ?? new MessageStatusResponse { MessageId = messageId, Status = "Unknown" };
            }
            
            return new MessageStatusResponse { MessageId = messageId, Status = "Error" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS status");
            return new MessageStatusResponse { MessageId = messageId, Status = "Error" };
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/sms/history?phoneNumber={phoneNumber}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MessageHistoryResponse>(responseContent, _jsonOptions) 
                       ?? new MessageHistoryResponse { Recipient = phoneNumber };
            }
            
            return new MessageHistoryResponse { Recipient = phoneNumber };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMS history");
            return new MessageHistoryResponse { Recipient = phoneNumber };
        }
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/sms/resend?messageId={messageId}", null);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<SmsResponse>(responseContent, _jsonOptions) 
                       ?? new SmsResponse(false, "Failed to deserialize response");
            }
            
            return new SmsResponse(false, $"Resend service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending SMS");
            return new SmsResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    // Admin and metrics methods - simplified implementations
    public IEnumerable<ProviderInfo> GetAvailableProviders() => Enumerable.Empty<ProviderInfo>();
    public void SwitchProvider(string providerKey) { }
    public async Task<SmsResponse> SendTestMessageAsync(string to) => new SmsResponse(false, "Not implemented");
    public void ReloadProviders() { }
    public void UpdateProviderConfiguration(string providerKey, Dictionary<string, string> config) { }
    public async Task<SummaryMetricsResponse> GetSummaryMetricsAsync() => new SummaryMetricsResponse();
    public async Task<DetailedMetricsResponse> GetDetailedMetricsAsync(DateTime? from, DateTime? to) => new DetailedMetricsResponse();
    public async Task<ErrorMetricsResponse> GetErrorMetricsAsync() => new ErrorMetricsResponse();
    public async Task<MonthlyStatisticsResponse> GetMonthlyStatisticsAsync() => new MonthlyStatisticsResponse();
}
