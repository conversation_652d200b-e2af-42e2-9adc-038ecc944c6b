<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="KavenegarDotNetCore" Version="1.0.7" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
        <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
    </ItemGroup>

</Project>
