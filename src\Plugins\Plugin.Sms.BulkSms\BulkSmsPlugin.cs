using Microsoft.Extensions.Logging;
using NotificationContract.Models;
using PluginCore.Interfaces;
using PluginCore.Models;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Plugin.Sms.BulkSms;

public class BulkSmsPlugin : ISmsPlugin
{
    private readonly ILogger<BulkSmsPlugin> _logger;
    private readonly HttpClient _httpClient;
    private Dictionary<string, PluginConfigurationItem> _configuration = new();
    private bool _isInitialized = false;

    public BulkSmsPlugin(ILogger<BulkSmsPlugin> logger, HttpClient httpClient)
    {
        _logger = logger;
        _httpClient = httpClient;
    }

    public string Name => "BulkSms SMS Plugin";
    public string Version => "1.0.0";
    public string Provider => "BulkSms";

    public async Task InitializeAsync(Dictionary<string, PluginConfigurationItem> configuration)
    {
        _configuration = configuration;
        
        // Configure HTTP client
        var apiUrl = GetConfigValue<string>("apiUrl") ?? "https://api.bulksms.com/v1";
        var username = GetConfigValue<string>("username");
        var password = GetConfigValue<string>("password");
        var timeout = GetConfigValue<int>("timeout", 30);

        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
        {
            throw new InvalidOperationException("BulkSms username and password are required");
        }

        _httpClient.BaseAddress = new Uri(apiUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(timeout);
        
        // Set basic authentication
        var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{username}:{password}"));
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", credentials);
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

        _isInitialized = true;
        _logger.LogInformation("BulkSms plugin initialized successfully");
    }

    public async Task<SmsResponse> SendAsync(SmsMessageRequest request)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var payload = new
            {
                to = request.PhoneNumber,
                body = request.Message,
                from = GetConfigValue<string>("from")
            };

            var json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/messages", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<BulkSmsResponse>(responseContent);
                return new SmsResponse(true, MessageId: result?.Id ?? Guid.NewGuid().ToString());
            }
            else
            {
                _logger.LogError("BulkSms API error: {StatusCode} - {Content}", response.StatusCode, responseContent);
                return new SmsResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending SMS via BulkSms");
            return new SmsResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request)
    {
        var results = new List<SmsResponse>();
        
        foreach (var sms in request.Messages)
        {
            var result = await SendAsync(sms);
            results.Add(result);
        }

        var successCount = results.Count(r => r.IsSuccess);
        return new BulkSmsResponse(
            successCount == results.Count,
            SuccessCount: successCount,
            FailureCount: results.Count - successCount,
            Results: results
        );
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        if (!_isInitialized)
            throw new InvalidOperationException("Plugin not initialized");

        try
        {
            var response = await _httpClient.GetAsync($"/messages/{messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<BulkSmsMessageStatus>(responseContent);
                return new MessageStatusResponse(true, Status: result?.Status?.Type ?? "Unknown");
            }
            else
            {
                return new MessageStatusResponse(false, ErrorMessage: $"API Error: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting message status from BulkSms");
            return new MessageStatusResponse(false, ErrorMessage: ex.Message);
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber)
    {
        // BulkSms doesn't provide message history by phone number in their standard API
        return new MessageHistoryResponse(false, ErrorMessage: "Message history not supported by BulkSms API");
    }

    public async Task<SmsResponse> ResendMessageAsync(string messageId)
    {
        // BulkSms doesn't support message resending
        return new SmsResponse(false, ErrorMessage: "Message resending not supported by BulkSms API");
    }

    public async Task<bool> ValidateConfigurationAsync()
    {
        try
        {
            var username = GetConfigValue<string>("username");
            var password = GetConfigValue<string>("password");
            
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return false;

            // Test API connection
            var response = await _httpClient.GetAsync("/profile");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private T? GetConfigValue<T>(string key, T? defaultValue = default)
    {
        if (_configuration.TryGetValue(key, out var configItem))
        {
            if (configItem.DefaultValue != null)
            {
                return (T)Convert.ChangeType(configItem.DefaultValue, typeof(T));
            }
        }
        return defaultValue;
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

// Response models for BulkSms API
public class BulkSmsResponse
{
    public string? Id { get; set; }
    public string? Type { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
    public string? Body { get; set; }
}

public class BulkSmsMessageStatus
{
    public string? Id { get; set; }
    public BulkSmsStatus? Status { get; set; }
}

public class BulkSmsStatus
{
    public string? Type { get; set; }
    public string? Subtype { get; set; }
}
