using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NotificationService.Plugins.Firebase.Configuration;
using PluginContract.Attributes;
using PluginContract.Enums;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;

namespace NotificationService.Plugins.Firebase;

[NotificationPlugin("Firebase", "1.0.0", "Firebase push notification provider plugin", PluginType.PushNotification, "NotificationService Team")]
public sealed class FirebasePushPlugin : BaseNotificationPlugin, IPushNotificationPlugin
{
    private FirebaseSettings? _settings;
    private FirebaseApp? _firebaseApp;

    public override PluginInfo PluginInfo => new(
        "Firebase",
        "1.0.0",
        "Firebase push notification provider plugin",
        PluginType.PushNotification,
        "NotificationService Team",
        true
    );

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        var settings = configuration.GetSection("Plugins:Firebase").Get<FirebaseSettings>();
        if (settings != null)
        {
            services.Configure<FirebaseSettings>(configuration.GetSection("Plugins:Firebase"));
        }
    }

    public override async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        var settings = configuration.GetSection("Plugins:Firebase").Get<FirebaseSettings>();
        
        if (settings == null)
        {
            Logger?.LogError("Firebase settings not found in configuration");
            return false;
        }

        if (string.IsNullOrWhiteSpace(settings.ServiceAccountKeyPath))
        {
            Logger?.LogError("Firebase service account key path is required");
            return false;
        }

        if (!File.Exists(settings.ServiceAccountKeyPath))
        {
            Logger?.LogError("Firebase service account key file not found: {Path}", settings.ServiceAccountKeyPath);
            return false;
        }

        return true;
    }

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);
        
        _settings = GetConfiguration<FirebaseSettings>();
        if (_settings != null)
        {
            try
            {
                var credential = GoogleCredential.FromFile(_settings.ServiceAccountKeyPath);
                
                _firebaseApp = FirebaseApp.Create(new AppOptions()
                {
                    Credential = credential,
                    ProjectId = _settings.ProjectId
                }, $"FirebasePlugin_{Guid.NewGuid()}");

                Logger?.LogInformation("Firebase push notification plugin initialized successfully");
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Failed to initialize Firebase app");
                throw;
            }
        }
        else
        {
            throw new InvalidOperationException("Failed to load Firebase settings");
        }
    }

    public async Task<NotificationResponse> SendPushNotificationAsync(PushNotificationRequest request, CancellationToken cancellationToken = default)
    {
        if (_firebaseApp == null || _settings == null)
        {
            return new PushNotificationResponse(false, ErrorMessage: "Plugin not properly initialized");
        }

        try
        {
            Logger?.LogInformation("Sending push notification to device {DeviceToken} via Firebase", request.DeviceToken);

            var message = new Message()
            {
                Token = request.DeviceToken,
                Notification = new Notification()
                {
                    Title = request.Title,
                    Body = request.Body
                }
            };

            // Add custom data if provided
            if (request.Data != null && request.Data.Any())
            {
                message.Data = request.Data.ToDictionary(
                    kvp => kvp.Key, 
                    kvp => kvp.Value?.ToString() ?? string.Empty
                );
            }

            var messaging = FirebaseMessaging.GetMessaging(_firebaseApp);
            var messageId = await messaging.SendAsync(message, _settings.DryRun, cancellationToken);

            Logger?.LogInformation("Push notification sent successfully via Firebase. MessageId: {MessageId}", messageId);

            return new PushNotificationResponse(
                true,
                MessageId: messageId,
                ResponseData: new Dictionary<string, object>
                {
                    ["DryRun"] = _settings.DryRun,
                    ["ProjectId"] = _settings.ProjectId ?? string.Empty
                }
            );
        }
        catch (FirebaseMessagingException ex)
        {
            Logger?.LogError(ex, "Firebase messaging error: {ErrorCode} - {Message}", ex.ErrorCode, ex.Message);
            return new PushNotificationResponse(false, ErrorMessage: $"Firebase Error [{ex.ErrorCode}]: {ex.Message}");
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "Unexpected error sending push notification via Firebase");
            return new PushNotificationResponse(false, ErrorMessage: $"Unexpected error: {ex.Message}");
        }
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        if (request is PushNotificationRequest pushRequest)
        {
            return await SendPushNotificationAsync(pushRequest, cancellationToken);
        }

        return new PushNotificationResponse(false, ErrorMessage: "Invalid request type for push notification plugin");
    }

    public override async Task<bool> HealthCheckAsync()
    {
        if (_firebaseApp == null || _settings == null)
        {
            return false;
        }

        try
        {
            // Perform a simple validation by creating a test message (without sending)
            var testMessage = new Message()
            {
                Token = "test-token",
                Notification = new Notification()
                {
                    Title = "Health Check",
                    Body = "Test message for health check"
                }
            };

            var messaging = FirebaseMessaging.GetMessaging(_firebaseApp);
            
            // Validate the message without sending (dry run)
            await messaging.SendAsync(testMessage, dryRun: true);
            return true;
        }
        catch
        {
            return false;
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && _firebaseApp != null)
        {
            _firebaseApp.Delete();
            _firebaseApp = null;
        }
        base.Dispose(disposing);
    }
}
