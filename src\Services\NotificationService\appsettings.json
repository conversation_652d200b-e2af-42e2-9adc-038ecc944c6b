{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "RabbitMqSetting": {"Url": "", "UserName": "", "Password": ""}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}, "ElasticSearch": {"Uri": "http://localhost:9200"}, "Plugins": {"PluginsDirectory": "plugins", "Kavenegar": {"ApiKey": "your-kavenegar-api-key", "SenderNumber": "your-sender-number", "IsEnabled": true, "TimeoutSeconds": 30, "RetryCount": 3}, "SMTP": {"Host": "smtp.gmail.com", "Port": 587, "UseSsl": true, "Username": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "Notification Service", "IsEnabled": true, "TimeoutSeconds": 30}, "Firebase": {"ServiceAccountKeyPath": "path/to/firebase-service-account.json", "ProjectId": "your-firebase-project-id", "IsEnabled": true, "TimeoutSeconds": 30, "DryRun": false}}}