{"version": 2, "dgSpecHash": "WEr28K7wbF7B8da578Muypizf2k5TS5mYii9jEoCXRFIgU6fQRXkrV0OLbCbOdOXqE9X/HGLgP/+O3mEYEciMw==", "success": true, "projectFilePath": "/Users/<USER>/Projects/NotificationService/src/EmailService/EmailService.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/bouncycastle.cryptography/2.2.1/bouncycastle.cryptography.2.2.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/mailkit/4.1.0/mailkit.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit/8.1.1-develop.1522/masstransit.8.1.1-develop.1522.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit.abstractions/8.1.1-develop.1522/masstransit.abstractions.8.1.1-develop.1522.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit.aspnetcore/7.3.1/masstransit.aspnetcore.7.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit.extensions.dependencyinjection/7.3.1/masstransit.extensions.dependencyinjection.7.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/masstransit.rabbitmq/8.1.1-develop.1522/masstransit.rabbitmq.8.1.1-develop.1522.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.openapi/7.0.1/microsoft.aspnetcore.openapi.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/7.0.0/microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/7.0.0/microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks/7.0.1/microsoft.extensions.diagnostics.healthchecks.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.healthchecks.abstractions/7.0.1/microsoft.extensions.diagnostics.healthchecks.abstractions.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/7.0.0/microsoft.extensions.fileproviders.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.hosting.abstractions/7.0.0/microsoft.extensions.hosting.abstractions.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/7.0.1/microsoft.extensions.logging.abstractions.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/7.0.1/microsoft.extensions.options.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/7.0.0/microsoft.extensions.primitives.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.openapi/1.4.3/microsoft.openapi.1.4.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/mimekit/4.1.0/mimekit.4.1.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/rabbitmq.client/6.5.0/rabbitmq.client.6.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.4.0/swashbuckle.aspnetcore.6.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.4.0/swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.4.0/swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.4.0/swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/7.0.1/system.diagnostics.diagnosticsource.7.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.formats.asn1/7.0.0/system.formats.asn1.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.5/system.memory.4.5.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit/4.7.0/system.reflection.emit.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.emit.lightweight/4.7.0/system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.security.cryptography.pkcs/7.0.2/system.security.cryptography.pkcs.7.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encoding.codepages/7.0.0/system.text.encoding.codepages.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/7.0.0/system.text.encodings.web.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/7.0.3/system.text.json.7.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.channels/7.0.0/system.threading.channels.7.0.0.nupkg.sha512"], "logs": []}