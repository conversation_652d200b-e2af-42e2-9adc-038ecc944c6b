using NotificationContract.Models;
using NotificationService.Gateways.Interfaces;
using System.Text;
using System.Text.Json;

namespace NotificationService.Gateways;

public class EmailGatewayClient : IEmailGateway
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<EmailGatewayClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public EmailGatewayClient(HttpClient httpClient, ILogger<EmailGatewayClient> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<EmailResponse> SendEmailAsync(EmailMessageRequest message)
    {
        try
        {
            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/email/send", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<EmailResponse>(responseContent, _jsonOptions) 
                       ?? new EmailResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Email service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new EmailResponse(false, $"Email service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling email service");
            return new EmailResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public async Task<BulkEmailResponse> SendBulkEmailAsync(BulkEmailRequest bulk)
    {
        try
        {
            var json = JsonSerializer.Serialize(bulk, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/email/send/bulk", content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<BulkEmailResponse>(responseContent, _jsonOptions) 
                       ?? new BulkEmailResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Bulk email service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new BulkEmailResponse(false, $"Bulk email service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling bulk email service");
            return new BulkEmailResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public async Task<MessageStatusResponse> GetMessageStatusAsync(string messageId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/email/status?messageId={messageId}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MessageStatusResponse>(responseContent, _jsonOptions) 
                       ?? new MessageStatusResponse { MessageId = messageId, Status = "Unknown" };
            }
            
            _logger.LogError("Email status service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new MessageStatusResponse { MessageId = messageId, Status = "Error" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email status");
            return new MessageStatusResponse { MessageId = messageId, Status = "Error" };
        }
    }

    public async Task<MessageHistoryResponse> GetMessageHistoryAsync(string email)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/email/history?email={email}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MessageHistoryResponse>(responseContent, _jsonOptions) 
                       ?? new MessageHistoryResponse { Recipient = email };
            }
            
            _logger.LogError("Email history service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new MessageHistoryResponse { Recipient = email };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email history");
            return new MessageHistoryResponse { Recipient = email };
        }
    }

    public async Task<EmailResponse> ResendMessageAsync(string messageId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/email/resend?messageId={messageId}", null);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<EmailResponse>(responseContent, _jsonOptions) 
                       ?? new EmailResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Email resend service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new EmailResponse(false, $"Resend service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resending email");
            return new EmailResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public IEnumerable<ProviderInfo> GetAvailableProviders()
    {
        try
        {
            var response = _httpClient.GetAsync("api/email/admin/providers").Result;
            var responseContent = response.Content.ReadAsStringAsync().Result;

            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<IEnumerable<ProviderInfo>>(responseContent, _jsonOptions)
                       ?? Enumerable.Empty<ProviderInfo>();
            }

            _logger.LogError("Email providers service returned error: {StatusCode} - {Content}",
                response.StatusCode, responseContent);
            return Enumerable.Empty<ProviderInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email providers");
            return Enumerable.Empty<ProviderInfo>();
        }
    }

    public void SwitchProvider(string providerKey)
    {
        try
        {
            var response = _httpClient.PostAsync($"api/email/admin/switch?providerKey={providerKey}", null).Result;
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Email switch provider returned error: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error switching email provider");
        }
    }

    public async Task<EmailResponse> SendTestMessageAsync(string to)
    {
        try
        {
            var response = await _httpClient.PostAsync($"api/email/admin/test?to={to}", null);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<EmailResponse>(responseContent, _jsonOptions) 
                       ?? new EmailResponse(false, "Failed to deserialize response");
            }
            
            _logger.LogError("Email test service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new EmailResponse(false, $"Test service error: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test email");
            return new EmailResponse(false, $"Service call failed: {ex.Message}");
        }
    }

    public void ReloadProviders()
    {
        try
        {
            var response = _httpClient.PostAsync("api/email/admin/reload", null).Result;
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Email reload providers returned error: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading email providers");
        }
    }

    public void UpdateProviderConfiguration(string providerKey, Dictionary<string, string> config)
    {
        try
        {
            var json = JsonSerializer.Serialize(config, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = _httpClient.PostAsync($"api/email/admin/config/update?providerKey={providerKey}", content).Result;
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Email update config returned error: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating email provider configuration");
        }
    }

    public async Task<SummaryMetricsResponse> GetSummaryMetricsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/email/metrics/summary");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<SummaryMetricsResponse>(responseContent, _jsonOptions) 
                       ?? new SummaryMetricsResponse();
            }
            
            _logger.LogError("Email metrics service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new SummaryMetricsResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email metrics");
            return new SummaryMetricsResponse();
        }
    }

    public async Task<DetailedMetricsResponse> GetDetailedMetricsAsync(DateTime? from, DateTime? to)
    {
        try
        {
            var query = "";
            if (from.HasValue) query += $"from={from.Value:yyyy-MM-dd}&";
            if (to.HasValue) query += $"to={to.Value:yyyy-MM-dd}&";
            
            var response = await _httpClient.GetAsync($"api/email/metrics/detailed?{query.TrimEnd('&')}");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<DetailedMetricsResponse>(responseContent, _jsonOptions) 
                       ?? new DetailedMetricsResponse();
            }
            
            _logger.LogError("Email detailed metrics service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new DetailedMetricsResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting detailed email metrics");
            return new DetailedMetricsResponse();
        }
    }

    public async Task<ErrorMetricsResponse> GetErrorMetricsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/email/metrics/errors");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<ErrorMetricsResponse>(responseContent, _jsonOptions) 
                       ?? new ErrorMetricsResponse();
            }
            
            _logger.LogError("Email error metrics service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new ErrorMetricsResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email error metrics");
            return new ErrorMetricsResponse();
        }
    }

    public async Task<MonthlyStatisticsResponse> GetMonthlyStatisticsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("api/email/metrics/monthly");
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<MonthlyStatisticsResponse>(responseContent, _jsonOptions) 
                       ?? new MonthlyStatisticsResponse();
            }
            
            _logger.LogError("Email monthly stats service returned error: {StatusCode} - {Content}", 
                response.StatusCode, responseContent);
            return new MonthlyStatisticsResponse();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email monthly statistics");
            return new MonthlyStatisticsResponse();
        }
    }
}
