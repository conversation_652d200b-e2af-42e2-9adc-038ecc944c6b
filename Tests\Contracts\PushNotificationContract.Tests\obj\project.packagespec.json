﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Documents\\NotificationService-master\\Tests\\Contracts\\PushNotificationContract.Tests\\PushNotificationContract.Tests.csproj","projectName":"PushNotificationContract.Tests","projectPath":"C:\\Users\\<USER>\\Documents\\NotificationService-master\\Tests\\Contracts\\PushNotificationContract.Tests\\PushNotificationContract.Tests.csproj","outputPath":"C:\\Users\\<USER>\\Documents\\NotificationService-master\\Tests\\Contracts\\PushNotificationContract.Tests\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\Documents\\NotificationService-master\\src\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj":{"projectPath":"C:\\Users\\<USER>\\Documents\\NotificationService-master\\src\\Contracts\\PushNotificationContract\\PushNotificationContract.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"10.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.14.1, )"},"coverlet.collector":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[6.0.4, )"},"xunit":{"target":"Package","version":"[2.9.3, )"},"xunit.runner.visualstudio":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[3.1.1, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.NETCore.App.Ref","version":"[9.0.5, 9.0.5]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[9.0.5, 9.0.5]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.5.25277.114/PortableRuntimeIdentifierGraph.json"}}