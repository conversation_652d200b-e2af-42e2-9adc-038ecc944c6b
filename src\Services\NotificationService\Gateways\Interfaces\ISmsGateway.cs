using NotificationContract.Models;

namespace NotificationService.Gateways.Interfaces;

public interface ISmsGateway
{
    // Core SMS operations
    Task<SmsResponse> SendSmsAsync(SmsMessageRequest message);
    Task<BulkSmsResponse> SendBulkSmsAsync(BulkSmsRequest bulk);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber);
    Task<SmsResponse> ResendMessageAsync(string messageId);

    // Admin operations
    IEnumerable<ProviderInfo> GetAvailableProviders();
    void SwitchProvider(string providerKey);
    Task<SmsResponse> SendTestMessageAsync(string to);
    void ReloadProviders();
    void UpdateProviderConfiguration(string providerKey, Dictionary<string, string> config);

    // Metrics operations
    Task<SummaryMetricsResponse> GetSummaryMetricsAsync();
    Task<DetailedMetricsResponse> GetDetailedMetricsAsync(DateTime? from, DateTime? to);
    Task<ErrorMetricsResponse> GetErrorMetricsAsync();
    Task<MonthlyStatisticsResponse> GetMonthlyStatisticsAsync();
}
