<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
        <AssemblyName>NotifyMasterApi</AssemblyName>
        <RootNamespace>NotifyMasterApi</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MassTransit" Version="8.5.0" />
        <PackageReference Include="MassTransit.AspNetCore" Version="7.3.1" />
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
        <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.1" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Contracts\NotificationContract\NotificationContract.csproj" />
      <ProjectReference Include="..\..\Contracts\PluginContract\PluginContract.csproj" />
      <ProjectReference Include="..\..\Core\PluginCore\PluginCore.csproj" />
      <ProjectReference Include="..\..\Libraries\EmailService.Library\EmailService.Library.csproj" />
      <ProjectReference Include="..\..\Libraries\SmsService.Library\SmsService.Library.csproj" />
      <ProjectReference Include="..\..\Libraries\PushNotificationService.Library\PushNotificationService.Library.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Presentation" />
    </ItemGroup>

</Project>
