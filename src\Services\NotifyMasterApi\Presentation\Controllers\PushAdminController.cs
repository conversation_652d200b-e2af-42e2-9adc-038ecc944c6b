using Microsoft.AspNetCore.Mvc;
using PushNotificationService.Library.Interfaces;
using NotificationService.Services;
using NotificationContract.Enums;
using PluginCore.Interfaces;

namespace NotificationService.Controllers;

/// <summary>
/// Push notification service administration controller
/// </summary>
[ApiController]
[Route("api/push/admin")]
[Produces("application/json")]
[Tags("Push Administration")]
public class PushAdminController : ControllerBase
{
    private readonly IPushNotificationService _pushService;
    private readonly INotificationLoggingService _loggingService;
    private readonly IPluginManager _pluginManager;
    private readonly ILogger<PushAdminController> _logger;

    public PushAdminController(
        IPushNotificationService pushService,
        INotificationLoggingService loggingService,
        IPluginManager pluginManager,
        ILogger<PushAdminController> logger)
    {
        _pushService = pushService;
        _loggingService = loggingService;
        _pluginManager = pluginManager;
        _logger = logger;
    }

    /// <summary>
    /// Get push notification service status and health information
    /// </summary>
    /// <returns>Service status including provider health and configuration</returns>
    /// <response code="200">Service status retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("status")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetServiceStatus()
    {
        try
        {
            var serviceStatus = await _pushService.GetServiceStatusAsync();
            var pluginStatus = await _pluginManager.GetPluginStatusAsync("Push");
            
            return Ok(new
            {
                Service = serviceStatus,
                Plugins = pluginStatus,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification service status");
            return StatusCode(500, new { Error = "Failed to get service status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get list of available push notification platforms and their configurations
    /// </summary>
    /// <returns>List of push platforms with their current status and settings</returns>
    /// <response code="200">Push platforms retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("platforms")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetPushPlatforms()
    {
        try
        {
            var platforms = await _pushService.GetPlatformsAsync();
            var plugins = await _pluginManager.GetLoadedPluginsAsync("Push");
            
            return Ok(new
            {
                Platforms = platforms,
                LoadedPlugins = plugins,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification platforms");
            return StatusCode(500, new { Error = "Failed to get push platforms", Message = ex.Message });
        }
    }

    /// <summary>
    /// Configure push notification platform settings
    /// </summary>
    /// <param name="platform">Platform name (iOS, Android, Web)</param>
    /// <param name="configuration">Platform configuration settings</param>
    /// <returns>Configuration result</returns>
    /// <response code="200">Platform configured successfully</response>
    /// <response code="400">Invalid configuration</response>
    /// <response code="404">Platform not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("platforms/{platform}/configure")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> ConfigurePlatform(string platform, [FromBody] object configuration)
    {
        try
        {
            var result = await _pushService.ConfigurePlatformAsync(platform, configuration);
            
            if (!result.Success)
            {
                return BadRequest(new { Error = result.ErrorMessage });
            }
            
            return Ok(new
            {
                Message = "Platform configured successfully",
                Platform = platform,
                Configuration = result.Data,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { Error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring push notification platform {Platform}", platform);
            return StatusCode(500, new { Error = "Failed to configure platform", Message = ex.Message });
        }
    }

    /// <summary>
    /// Test push notification platform connectivity and configuration
    /// </summary>
    /// <param name="platform">Platform name to test</param>
    /// <param name="testToken">Test device token (optional)</param>
    /// <returns>Test results including connectivity and send test</returns>
    /// <response code="200">Test completed successfully</response>
    /// <response code="404">Platform not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("platforms/{platform}/test")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> TestPlatform(string platform, [FromQuery] string? testToken = null)
    {
        try
        {
            var testResult = await _pushService.TestPlatformAsync(platform, testToken);
            
            return Ok(new
            {
                Platform = platform,
                TestResults = testResult,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { Error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing push notification platform {Platform}", platform);
            return StatusCode(500, new { Error = "Failed to test platform", Message = ex.Message });
        }
    }

    /// <summary>
    /// Enable or disable a push notification platform
    /// </summary>
    /// <param name="platform">Platform name</param>
    /// <param name="enabled">Enable or disable the platform</param>
    /// <returns>Platform status update result</returns>
    /// <response code="200">Platform status updated successfully</response>
    /// <response code="404">Platform not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("platforms/{platform}/status")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdatePlatformStatus(string platform, [FromQuery] bool enabled)
    {
        try
        {
            var result = await _pushService.UpdatePlatformStatusAsync(platform, enabled);
            
            return Ok(new
            {
                Platform = platform,
                Enabled = enabled,
                Message = $"Platform {(enabled ? "enabled" : "disabled")} successfully",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { Error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating push notification platform status {Platform}", platform);
            return StatusCode(500, new { Error = "Failed to update platform status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get push notification service configuration settings
    /// </summary>
    /// <returns>Current service configuration</returns>
    /// <response code="200">Configuration retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("configuration")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetConfiguration()
    {
        try
        {
            var configuration = await _pushService.GetConfigurationAsync();
            
            return Ok(new
            {
                Configuration = configuration,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification service configuration");
            return StatusCode(500, new { Error = "Failed to get configuration", Message = ex.Message });
        }
    }

    /// <summary>
    /// Update push notification service configuration settings
    /// </summary>
    /// <param name="configuration">New configuration settings</param>
    /// <returns>Configuration update result</returns>
    /// <response code="200">Configuration updated successfully</response>
    /// <response code="400">Invalid configuration</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("configuration")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdateConfiguration([FromBody] object configuration)
    {
        try
        {
            var result = await _pushService.UpdateConfigurationAsync(configuration);
            
            if (!result.Success)
            {
                return BadRequest(new { Error = result.ErrorMessage });
            }
            
            return Ok(new
            {
                Message = "Configuration updated successfully",
                Configuration = result.Data,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating push notification service configuration");
            return StatusCode(500, new { Error = "Failed to update configuration", Message = ex.Message });
        }
    }

    /// <summary>
    /// Clear push notification service cache and reset connections
    /// </summary>
    /// <returns>Cache clear result</returns>
    /// <response code="200">Cache cleared successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("cache/clear")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> ClearCache()
    {
        try
        {
            await _pushService.ClearCacheAsync();
            
            return Ok(new
            {
                Message = "Push notification service cache cleared successfully",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing push notification service cache");
            return StatusCode(500, new { Error = "Failed to clear cache", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get push notification queue status and management
    /// </summary>
    /// <returns>Queue status and pending messages</returns>
    /// <response code="200">Queue status retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("queue")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetQueueStatus()
    {
        try
        {
            var queueStatus = await _pushService.GetQueueStatusAsync();
            
            return Ok(new
            {
                QueueStatus = queueStatus,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting push notification queue status");
            return StatusCode(500, new { Error = "Failed to get queue status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Purge push notification queue (remove all pending messages)
    /// </summary>
    /// <param name="confirm">Confirmation flag to prevent accidental purge</param>
    /// <returns>Queue purge result</returns>
    /// <response code="200">Queue purged successfully</response>
    /// <response code="400">Confirmation required</response>
    /// <response code="500">Internal server error</response>
    [HttpDelete("queue/purge")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> PurgeQueue([FromQuery] bool confirm = false)
    {
        try
        {
            if (!confirm)
            {
                return BadRequest(new { Error = "Confirmation required. Add ?confirm=true to purge the queue." });
            }

            var result = await _pushService.PurgeQueueAsync();
            
            return Ok(new
            {
                Message = "Push notification queue purged successfully",
                PurgedCount = result.PurgedCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error purging push notification queue");
            return StatusCode(500, new { Error = "Failed to purge queue", Message = ex.Message });
        }
    }

    /// <summary>
    /// Manage device tokens (cleanup invalid tokens)
    /// </summary>
    /// <param name="platform">Platform to clean up (optional)</param>
    /// <param name="dryRun">Perform a dry run without actually removing tokens</param>
    /// <returns>Device token cleanup result</returns>
    /// <response code="200">Device token cleanup completed</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("device-tokens/cleanup")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> CleanupDeviceTokens(
        [FromQuery] string? platform = null,
        [FromQuery] bool dryRun = true)
    {
        try
        {
            var result = await _pushService.CleanupDeviceTokensAsync(platform, dryRun);
            
            return Ok(new
            {
                Message = dryRun ? "Device token cleanup simulation completed" : "Device token cleanup completed",
                Platform = platform,
                DryRun = dryRun,
                Results = result,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up device tokens");
            return StatusCode(500, new { Error = "Failed to cleanup device tokens", Message = ex.Message });
        }
    }

    /// <summary>
    /// Get certificate status for iOS push notifications
    /// </summary>
    /// <returns>Certificate validity and expiration information</returns>
    /// <response code="200">Certificate status retrieved successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("certificates/ios")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> GetIosCertificateStatus()
    {
        try
        {
            var certificateStatus = await _pushService.GetIosCertificateStatusAsync();
            
            return Ok(new
            {
                CertificateStatus = certificateStatus,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting iOS certificate status");
            return StatusCode(500, new { Error = "Failed to get certificate status", Message = ex.Message });
        }
    }

    /// <summary>
    /// Update iOS push notification certificate
    /// </summary>
    /// <param name="certificate">Certificate file and configuration</param>
    /// <returns>Certificate update result</returns>
    /// <response code="200">Certificate updated successfully</response>
    /// <response code="400">Invalid certificate</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("certificates/ios/update")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdateIosCertificate([FromForm] IFormFile certificate)
    {
        try
        {
            if (certificate == null || certificate.Length == 0)
            {
                return BadRequest(new { Error = "Certificate file is required" });
            }

            var result = await _pushService.UpdateIosCertificateAsync(certificate);
            
            if (!result.Success)
            {
                return BadRequest(new { Error = result.ErrorMessage });
            }
            
            return Ok(new
            {
                Message = "iOS certificate updated successfully",
                CertificateInfo = result.Data,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating iOS certificate");
            return StatusCode(500, new { Error = "Failed to update certificate", Message = ex.Message });
        }
    }
}
