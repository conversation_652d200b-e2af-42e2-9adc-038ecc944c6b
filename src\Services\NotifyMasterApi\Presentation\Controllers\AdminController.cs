using Microsoft.AspNetCore.Mvc;
using NotificationContract.Enums;
using NotificationService.Services;
using PluginCore.Interfaces;
using System.IO.Compression;

namespace NotificationService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AdminController : ControllerBase
{
    private readonly IPluginManager _pluginManager;
    private readonly INotificationLoggingService _loggingService;
    private readonly INotificationQueueService _queueService;
    private readonly ILogger<AdminController> _logger;
    private readonly string _pluginsDirectory;

    public AdminController(
        IPluginManager pluginManager,
        INotificationLoggingService loggingService,
        INotificationQueueService queueService,
        ILogger<AdminController> logger,
        IConfiguration configuration)
    {
        _pluginManager = pluginManager;
        _loggingService = loggingService;
        _queueService = queueService;
        _logger = logger;
        _pluginsDirectory = configuration.GetValue<string>("PluginsDirectory") ?? "Plugins";
    }

    // Plugin Management Endpoints
    [HttpGet("plugins")]
    public ActionResult GetLoadedPlugins()
    {
        var plugins = _pluginManager.GetLoadedPlugins();
        return Ok(plugins);
    }

    [HttpPost("plugins/upload")]
    public async Task<ActionResult> UploadPlugin(IFormFile pluginFile)
    {
        try
        {
            if (pluginFile == null || pluginFile.Length == 0)
            {
                return BadRequest(new { Error = "No plugin file provided" });
            }

            if (!pluginFile.FileName.EndsWith(".zip"))
            {
                return BadRequest(new { Error = "Plugin must be a ZIP file" });
            }

            var pluginName = Path.GetFileNameWithoutExtension(pluginFile.FileName);
            var pluginPath = Path.Combine(_pluginsDirectory, pluginName);

            // Create plugin directory
            if (Directory.Exists(pluginPath))
            {
                return Conflict(new { Error = $"Plugin {pluginName} already exists" });
            }

            Directory.CreateDirectory(pluginPath);

            // Extract ZIP file
            using (var stream = pluginFile.OpenReadStream())
            using (var archive = new ZipArchive(stream, ZipArchiveMode.Read))
            {
                archive.ExtractToDirectory(pluginPath);
            }

            // Load the plugin
            var success = await _pluginManager.LoadPluginAsync(pluginPath);
            
            if (success)
            {
                _logger.LogInformation("Successfully uploaded and loaded plugin {PluginName}", pluginName);
                return Ok(new { Success = true, PluginName = pluginName, Message = "Plugin uploaded and loaded successfully" });
            }
            else
            {
                // Clean up if loading failed
                Directory.Delete(pluginPath, true);
                return BadRequest(new { Error = "Failed to load plugin after upload" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading plugin");
            return StatusCode(500, new { Error = "Failed to upload plugin", Message = ex.Message });
        }
    }

    [HttpPost("plugins/{pluginKey}/load")]
    public async Task<ActionResult> LoadPlugin(string pluginKey)
    {
        try
        {
            var pluginPath = Path.Combine(_pluginsDirectory, pluginKey);
            var success = await _pluginManager.LoadPluginAsync(pluginPath);
            
            if (success)
            {
                return Ok(new { Success = true, Message = $"Plugin {pluginKey} loaded successfully" });
            }
            else
            {
                return BadRequest(new { Error = $"Failed to load plugin {pluginKey}" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading plugin {PluginKey}", pluginKey);
            return StatusCode(500, new { Error = "Failed to load plugin", Message = ex.Message });
        }
    }

    [HttpPost("plugins/{pluginKey}/unload")]
    public async Task<ActionResult> UnloadPlugin(string pluginKey)
    {
        try
        {
            var success = await _pluginManager.UnloadPluginAsync(pluginKey);
            
            if (success)
            {
                return Ok(new { Success = true, Message = $"Plugin {pluginKey} unloaded successfully" });
            }
            else
            {
                return BadRequest(new { Error = $"Failed to unload plugin {pluginKey}" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unloading plugin {PluginKey}", pluginKey);
            return StatusCode(500, new { Error = "Failed to unload plugin", Message = ex.Message });
        }
    }

    [HttpDelete("plugins/{pluginKey}")]
    public async Task<ActionResult> DeletePlugin(string pluginKey)
    {
        try
        {
            // First unload the plugin
            await _pluginManager.UnloadPluginAsync(pluginKey);
            
            // Then delete the plugin directory
            var pluginPath = Path.Combine(_pluginsDirectory, pluginKey);
            if (Directory.Exists(pluginPath))
            {
                Directory.Delete(pluginPath, true);
            }

            return Ok(new { Success = true, Message = $"Plugin {pluginKey} deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting plugin {PluginKey}", pluginKey);
            return StatusCode(500, new { Error = "Failed to delete plugin", Message = ex.Message });
        }
    }

    [HttpPost("plugins/reload-all")]
    public async Task<ActionResult> ReloadAllPlugins()
    {
        try
        {
            await _pluginManager.LoadAllPluginsAsync();
            return Ok(new { Success = true, Message = "All plugins reloaded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reloading all plugins");
            return StatusCode(500, new { Error = "Failed to reload plugins", Message = ex.Message });
        }
    }

    // System Administration Endpoints
    [HttpGet("system/status")]
    public async Task<ActionResult> GetSystemStatus()
    {
        try
        {
            var queueLength = await _queueService.GetQueueLengthAsync();
            var processingCount = await _queueService.GetProcessingCountAsync();
            var failedCount = await _queueService.GetFailedCountAsync();
            var loadedPlugins = _pluginManager.GetLoadedPlugins().Count();

            return Ok(new
            {
                System = new
                {
                    Status = "Running",
                    Timestamp = DateTime.UtcNow,
                    LoadedPlugins = loadedPlugins
                },
                Queue = new
                {
                    QueueLength = queueLength,
                    ProcessingCount = processingCount,
                    FailedCount = failedCount
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system status");
            return StatusCode(500, new { Error = "Failed to get system status", Message = ex.Message });
        }
    }

    [HttpGet("logs/{messageId}")]
    public async Task<ActionResult> GetNotificationLog(string messageId)
    {
        var log = await _loggingService.GetNotificationLogAsync(messageId);
        if (log == null)
            return NotFound();
        return Ok(log);
    }

    [HttpGet("logs/history/{recipient}")]
    public async Task<ActionResult> GetNotificationHistory(string recipient, [FromQuery] int skip = 0, [FromQuery] int take = 50)
    {
        var history = await _loggingService.GetNotificationHistoryAsync(recipient, skip, take);
        return Ok(history);
    }

    [HttpGet("metrics")]
    public async Task<ActionResult> GetMetrics([FromQuery] NotificationType? type = null, [FromQuery] string? provider = null, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        var metrics = await _loggingService.GetMetricsAsync(type, provider, from, to);
        return Ok(metrics);
    }

    [HttpGet("errors")]
    public async Task<ActionResult> GetErrors([FromQuery] NotificationType? type = null, [FromQuery] string? provider = null, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null, [FromQuery] bool unresolved = false)
    {
        var errors = await _loggingService.GetErrorsAsync(type, provider, from, to, unresolved);
        return Ok(errors);
    }

    // Queue Management Endpoints
    [HttpGet("queue/status")]
    public async Task<ActionResult> GetQueueStatus()
    {
        try
        {
            var queueLength = await _queueService.GetQueueLengthAsync();
            var processingCount = await _queueService.GetProcessingCountAsync();
            var failedCount = await _queueService.GetFailedCountAsync();

            return Ok(new
            {
                QueueLength = queueLength,
                ProcessingCount = processingCount,
                FailedCount = failedCount,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting queue status");
            return StatusCode(500, new { Error = "Failed to get queue status", Message = ex.Message });
        }
    }

    [HttpPost("queue/retry-failed")]
    public async Task<ActionResult> RetryFailedNotifications()
    {
        try
        {
            var retriedCount = await _queueService.RetryFailedNotificationsAsync();
            return Ok(new { RetriedCount = retriedCount, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrying failed notifications");
            return StatusCode(500, new { Error = "Failed to retry notifications", Message = ex.Message });
        }
    }

    [HttpDelete("queue/clear-failed")]
    public async Task<ActionResult> ClearFailedNotifications()
    {
        try
        {
            var clearedCount = await _queueService.ClearFailedNotificationsAsync();
            return Ok(new { ClearedCount = clearedCount, Timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing failed notifications");
            return StatusCode(500, new { Error = "Failed to clear failed notifications", Message = ex.Message });
        }
    }
}
