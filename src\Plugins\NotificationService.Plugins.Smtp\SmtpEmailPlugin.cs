using MailKit.Net.Smtp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MimeKit;
using NotificationService.Plugins.Smtp.Configuration;
using PluginContract.Attributes;
using PluginContract.Enums;
using PluginContract.Interfaces;
using PluginContract.Models;
using PluginCore.Base;

namespace NotificationService.Plugins.Smtp;

[NotificationPlugin("SMTP", "1.0.0", "SMTP email provider plugin", PluginType.Email, "NotificationService Team")]
public sealed class SmtpEmailPlugin : BaseNotificationPlugin, IEmailPlugin
{
    private SmtpSettings? _settings;

    public override PluginInfo PluginInfo => new(
        "SMTP",
        "1.0.0",
        "SMTP email provider plugin for sending emails",
        PluginType.Email,
        "NotificationService Team",
        true
    );

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        var settings = configuration.GetSection("Plugins:SMTP").Get<SmtpSettings>();
        if (settings != null)
        {
            services.Configure<SmtpSettings>(configuration.GetSection("Plugins:SMTP"));
        }
    }

    public override async Task<bool> ValidateConfigurationAsync(IConfiguration configuration)
    {
        var settings = configuration.GetSection("Plugins:SMTP").Get<SmtpSettings>();
        
        if (settings == null)
        {
            Logger?.LogError("SMTP settings not found in configuration");
            return false;
        }

        if (string.IsNullOrWhiteSpace(settings.Host))
        {
            Logger?.LogError("SMTP host is required");
            return false;
        }

        if (string.IsNullOrWhiteSpace(settings.FromEmail))
        {
            Logger?.LogError("SMTP from email is required");
            return false;
        }

        if (settings.Port <= 0)
        {
            Logger?.LogError("SMTP port must be greater than 0");
            return false;
        }

        return true;
    }

    public override async Task InitializeAsync(IConfiguration configuration)
    {
        await base.InitializeAsync(configuration);
        
        _settings = GetConfiguration<SmtpSettings>();
        if (_settings != null)
        {
            Logger?.LogInformation("SMTP email plugin initialized successfully");
        }
        else
        {
            throw new InvalidOperationException("Failed to load SMTP settings");
        }
    }

    public async Task<NotificationResponse> SendEmailAsync(EmailRequest request, CancellationToken cancellationToken = default)
    {
        if (_settings == null)
        {
            return new EmailResponse(false, ErrorMessage: "Plugin not properly initialized");
        }

        try
        {
            Logger?.LogInformation("Sending email to {To} via SMTP", request.To);

            var message = new MimeMessage();
            message.From.Add(new MailboxAddress(_settings.FromName, _settings.FromEmail));
            message.To.Add(new MailboxAddress("", request.To));
            message.Subject = request.Subject;

            // Add CC recipients
            if (request.Cc != null)
            {
                foreach (var cc in request.Cc)
                {
                    message.Cc.Add(new MailboxAddress("", cc));
                }
            }

            // Add BCC recipients
            if (request.Bcc != null)
            {
                foreach (var bcc in request.Bcc)
                {
                    message.Bcc.Add(new MailboxAddress("", bcc));
                }
            }

            var bodyBuilder = new BodyBuilder();
            
            // Check if body is HTML
            if (request.Body.Contains("<html>") || request.Body.Contains("<HTML>"))
            {
                bodyBuilder.HtmlBody = request.Body;
            }
            else
            {
                bodyBuilder.TextBody = request.Body;
            }

            // Add attachments
            if (request.Attachments != null)
            {
                foreach (var attachment in request.Attachments)
                {
                    bodyBuilder.Attachments.Add(attachment.FileName, attachment.Content, ContentType.Parse(attachment.ContentType));
                }
            }

            message.Body = bodyBuilder.ToMessageBody();

            using var client = new SmtpClient();
            
            // Set timeout
            client.Timeout = _settings.TimeoutSeconds * 1000;
            
            await client.ConnectAsync(_settings.Host, _settings.Port, _settings.UseSsl, cancellationToken);
            
            if (!string.IsNullOrEmpty(_settings.Username) && !string.IsNullOrEmpty(_settings.Password))
            {
                await client.AuthenticateAsync(_settings.Username, _settings.Password, cancellationToken);
            }
            
            var messageId = await client.SendAsync(message, cancellationToken);
            await client.DisconnectAsync(true, cancellationToken);

            Logger?.LogInformation("Email sent successfully via SMTP. MessageId: {MessageId}", messageId);

            return new EmailResponse(
                true,
                MessageId: messageId,
                ResponseData: new Dictionary<string, object>
                {
                    ["Host"] = _settings.Host,
                    ["Port"] = _settings.Port,
                    ["UseSsl"] = _settings.UseSsl
                }
            );
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "Error sending email via SMTP");
            return new EmailResponse(false, ErrorMessage: $"SMTP Error: {ex.Message}");
        }
    }

    public override async Task<NotificationResponse> SendAsync(NotificationRequest request, CancellationToken cancellationToken = default)
    {
        if (request is EmailRequest emailRequest)
        {
            return await SendEmailAsync(emailRequest, cancellationToken);
        }

        return new EmailResponse(false, ErrorMessage: "Invalid request type for email plugin");
    }

    public override async Task<bool> HealthCheckAsync()
    {
        if (_settings == null)
        {
            return false;
        }

        try
        {
            using var client = new SmtpClient();
            client.Timeout = 5000; // 5 second timeout for health check
            
            await client.ConnectAsync(_settings.Host, _settings.Port, _settings.UseSsl);
            
            if (!string.IsNullOrEmpty(_settings.Username) && !string.IsNullOrEmpty(_settings.Password))
            {
                await client.AuthenticateAsync(_settings.Username, _settings.Password);
            }
            
            await client.DisconnectAsync(true);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
