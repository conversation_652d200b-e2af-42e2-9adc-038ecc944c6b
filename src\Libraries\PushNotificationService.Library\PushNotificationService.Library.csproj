<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Contracts\PushNotificationContract\PushNotificationContract.csproj" />
        <ProjectReference Include="..\..\Contracts\NotificationContract\NotificationContract.csproj" />
    </ItemGroup>

</Project>
