<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MailKit" Version="4.13.0" />
        <PackageReference Include="MassTransit" Version="8.5.0" />
        <PackageReference Include="MassTransit.AspNetCore" Version="7.3.1" />
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
        <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="10.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Contracts\EmailContract\EmailContract.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Presentation" />
    </ItemGroup>

</Project>
