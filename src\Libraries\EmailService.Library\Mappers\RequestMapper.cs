using EmailContract.Models;
using EmailService.Library.Configuration;
using MimeKit;
using NotificationContract.Models;

namespace EmailService.Library.Mappers;

public static class RequestMapper
{
    public static MimeMessage MapToMailMessage(this EmailMessageRequest request, MailSetting mailSetting)
    {
        var message = new MimeMessage();
        
        message.From.Add(new MailboxAddress(mailSetting.FromName, mailSetting.FromEmail));
        message.To.Add(new MailboxAddress("", request.To));
        
        if (!string.IsNullOrEmpty(request.Subject))
            message.Subject = request.Subject;
            
        var bodyBuilder = new BodyBuilder();
        
        if (request.IsHtml)
        {
            bodyBuilder.HtmlBody = request.Body;
        }
        else
        {
            bodyBuilder.TextBody = request.Body;
        }
        
        // Handle attachments if any
        if (request.Attachments?.Any() == true)
        {
            foreach (var attachment in request.Attachments)
            {
                if (!string.IsNullOrEmpty(attachment.FilePath) && File.Exists(attachment.FilePath))
                {
                    bodyBuilder.Attachments.Add(attachment.FilePath);
                }
                else if (attachment.Content?.Length > 0)
                {
                    bodyBuilder.Attachments.Add(attachment.FileName, attachment.Content, ContentType.Parse(attachment.ContentType ?? "application/octet-stream"));
                }
            }
        }
        
        message.Body = bodyBuilder.ToMessageBody();
        
        return message;
    }
}
