using NotificationContract.Models;

namespace NotificationService.Gateways.Interfaces;

public interface IPushGateway
{
    // Core push notification operations
    Task<PushResponse> SendPushAsync(PushMessageRequest message);
    Task<BulkPushResponse> SendBulkPushAsync(BulkPushRequest bulk);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string deviceToken);
    Task<PushResponse> ResendMessageAsync(string messageId);

    // Admin operations
    IEnumerable<ProviderInfo> GetAvailableProviders();
    void SwitchProvider(string providerKey);
    Task<PushResponse> SendTestMessageAsync(string to);
    void ReloadProviders();
    void UpdateProviderConfiguration(string providerKey, Dictionary<string, string> config);

    // Metrics operations
    Task<SummaryMetricsResponse> GetSummaryMetricsAsync();
    Task<DetailedMetricsResponse> GetDetailedMetricsAsync(DateTime? from, DateTime? to);
    Task<ErrorMetricsResponse> GetErrorMetricsAsync();
    Task<MonthlyStatisticsResponse> GetMonthlyStatisticsAsync();
}
