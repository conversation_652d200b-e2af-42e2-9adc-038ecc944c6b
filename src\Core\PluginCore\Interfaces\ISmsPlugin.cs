using NotificationContract.Models;

namespace PluginCore.Interfaces;

public interface ISmsPlugin : IPlugin
{
    Task<SmsResponse> SendAsync(SmsMessageRequest request);
    Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber);
    Task<SmsResponse> ResendMessageAsync(string messageId);
    Task<bool> ValidateConfigurationAsync();
}
