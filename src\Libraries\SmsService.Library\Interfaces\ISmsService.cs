using NotificationContract.Models;

namespace SmsService.Library.Interfaces;

public interface ISmsService
{
    Task<SmsResponse> SendAsync(SmsMessageRequest request);
    Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber);
    Task<SmsResponse> ResendMessageAsync(string messageId);
}
