using NotificationContract.Models;

namespace SmsService.Library.Interfaces;

public interface ISmsService
{
    // Core SMS functionality
    Task<SmsResponse> SendAsync(SmsMessageRequest request);
    Task<BulkSmsResponse> SendBulkAsync(BulkSmsRequest request);
    Task<MessageStatusResponse> GetMessageStatusAsync(string messageId);
    Task<MessageHistoryResponse> GetMessageHistoryAsync(string phoneNumber);
    Task<SmsResponse> ResendMessageAsync(string messageId);

    // Admin functionality
    Task<object> GetServiceStatusAsync();
    Task<object> GetProvidersAsync();
    Task<ServiceResult> ConfigureProviderAsync(string provider, object configuration);
    Task<object> TestProviderAsync(string provider, string? testNumber = null);
    Task<ServiceResult> UpdateProviderStatusAsync(string provider, bool enabled);
    Task<object> GetConfigurationAsync();
    Task<ServiceResult> UpdateConfigurationAsync(object configuration);
    Task ClearCacheAsync();
    Task<object> GetQueueStatusAsync();
    Task<ServiceResult> PurgeQueueAsync();
    Task<object> GetRateLimitingAsync();
    Task<ServiceResult> UpdateRateLimitingAsync(object rateLimitConfig);

    // Metrics functionality
    Task<object> GetSummaryMetricsAsync();
    Task<object> GetDetailedMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null, string? country = null);
    Task<object> GetErrorMetricsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<object> GetMonthlyMetricsAsync(int months = 12);
    Task<object> GetPerformanceMetricsAsync();
    Task<object> GetDeliveryRateMetricsAsync(string? provider = null, string? country = null);
    Task<object> GetCostMetricsAsync(DateTime? startDate = null, DateTime? endDate = null, string? provider = null);
}


